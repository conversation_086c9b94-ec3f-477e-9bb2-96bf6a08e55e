
import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { MessageSquare, Clock, User } from 'lucide-react';
import { Comment } from '@/types';

interface EditHistoryPanelProps {
  entityType: 'jobsheet' | 'invoice' | 'service' | 'customer';
  entityId: string;
  comments: Comment[];
  onAddComment: (comment: Omit<Comment, 'id' | 'timestamp'>) => void;
  className?: string;
}

export const EditHistoryPanel = ({ 
  entityType, 
  entityId, 
  comments, 
  onAddComment, 
  className 
}: EditHistoryPanelProps) => {
  const [newComment, setNewComment] = useState('');
  const [commentType, setCommentType] = useState('Internal');

  const handleAddComment = () => {
    if (!newComment.trim()) return;

    onAddComment({
      entityType,
      entityId,
      content: newComment,
      author: 'Current User' // In a real app, this would be the logged-in user
    });

    setNewComment('');
  };

  const sortedComments = [...comments].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  return (
    <div className={`w-80 border-l bg-gray-50 ${className}`}>
      <Card className="h-full border-0 rounded-none">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2 text-lg">
            <MessageSquare className="h-5 w-5" />
            Edit & Comments
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Add Comment Section */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm font-medium">
              <MessageSquare className="h-4 w-4" />
              Add Comment
            </div>
            
            <Select value={commentType} onValueChange={setCommentType}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Internal">Internal</SelectItem>
                <SelectItem value="Customer">Customer</SelectItem>
                <SelectItem value="System">System</SelectItem>
              </SelectContent>
            </Select>

            <Textarea
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              placeholder={`Add a comment about this ${entityType}...`}
              className="min-h-[80px]"
            />

            <Button 
              onClick={handleAddComment}
              className="w-full bg-orange-500 hover:bg-orange-600"
              disabled={!newComment.trim()}
            >
              Add Comment
            </Button>
          </div>

          {/* Comments History */}
          <div className="space-y-3">
            <div className="flex items-center gap-2 text-sm font-medium">
              <Clock className="h-4 w-4" />
              History ({sortedComments.length})
            </div>

            <div className="space-y-3 max-h-96 overflow-y-auto">
              {sortedComments.length === 0 ? (
                <p className="text-sm text-gray-500 text-center py-4">
                  No comments yet
                </p>
              ) : (
                sortedComments.map((comment) => (
                  <Card key={comment.id} className="p-3">
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <User className="h-3 w-3" />
                          <span className="text-sm font-medium">{comment.author}</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {comment.author === 'System' ? 'System' : 'Internal'}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-700">{comment.content}</p>
                      <p className="text-xs text-gray-500">
                        {new Date(comment.timestamp).toLocaleString()}
                      </p>
                    </div>
                  </Card>
                ))
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
