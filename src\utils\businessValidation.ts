
import { Customer, Jobsheet, Service, Invoice, Expense } from '@/types';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export class BusinessValidator {
  static validateCustomer(customer: Partial<Customer>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!customer.name?.trim()) {
      errors.push('Customer name is required');
    }

    if (!customer.phone) {
      errors.push('Phone number is required');
    } else if (isNaN(customer.phone)) {
      errors.push('Invalid phone number format');
    }

    if (customer.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(customer.email)) {
      errors.push('Invalid email format');
    }

    if (!customer.address?.trim()) {
      warnings.push('Address is recommended for service delivery');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  static validateJobsheet(jobsheet: Partial<Jobsheet>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!jobsheet.deviceType?.trim()) {
      errors.push('Device type is required');
    }

    if (!jobsheet.brand?.trim()) {
      errors.push('Device brand is required');
    }

    if (!jobsheet.reportedIssues?.trim()) {
      errors.push('Reported issues description is required');
    }

    if (!jobsheet.estimatedCost || jobsheet.estimatedCost <= 0) {
      errors.push('Estimated cost must be greater than 0');
    }

    if (jobsheet.advancePayment && jobsheet.estimatedCost && 
        jobsheet.advancePayment > jobsheet.estimatedCost) {
      warnings.push('Advance payment exceeds estimated cost');
    }

    if (!jobsheet.expectedDelivery) {
      errors.push('Expected delivery date is required');
    } else {
      const deliveryDate = new Date(jobsheet.expectedDelivery);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      if (deliveryDate < today) {
        warnings.push('Expected delivery date is in the past');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  static validateService(service: Partial<Service>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!service.description?.trim()) {
      errors.push('Service description is required');
    }

    if (!service.cost || service.cost <= 0) {
      errors.push('Service cost must be greater than 0');
    }

    if (service.status === 'completed' && !service.technicianNotes?.trim()) {
      warnings.push('Technician notes recommended for completed services');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  static validateInvoice(invoice: Partial<Invoice>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!invoice.customerId) {
      errors.push('Customer is required');
    }

    if (!invoice.serviceIds || invoice.serviceIds.length === 0) {
      errors.push('At least one service must be included');
    }

    if (!invoice.subtotal || invoice.subtotal <= 0) {
      errors.push('Subtotal must be greater than 0');
    }

    if (!invoice.issueDate) {
      errors.push('Issue date is required');
    }

    if (!invoice.dueDate) {
      errors.push('Due date is required');
    } else if (invoice.issueDate) {
      const issueDate = new Date(invoice.issueDate);
      const dueDate = new Date(invoice.dueDate);
      
      if (dueDate <= issueDate) {
        errors.push('Due date must be after issue date');
      }
    }

    if (invoice.status === 'overdue') {
      const today = new Date();
      const dueDate = new Date(invoice.dueDate || '');
      
      if (dueDate >= today) {
        warnings.push('Invoice marked as overdue but due date has not passed');
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  static validateExpense(expense: Partial<Expense>): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    if (!expense.description?.trim()) {
      errors.push('Expense description is required');
    }

    if (!expense.amount || expense.amount <= 0) {
      errors.push('Expense amount must be greater than 0');
    }

    if (!expense.category) {
      errors.push('Expense category is required');
    }

    if (!expense.date) {
      errors.push('Expense date is required');
    }

    if (!expense.receiptAttached) {
      warnings.push('Receipt attachment recommended for expense tracking');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}
