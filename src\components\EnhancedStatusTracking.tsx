import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { useEnhancedAppContext } from "@/context";
import { CheckCircle, Clock, AlertTriangle, Package } from "lucide-react";

export const EnhancedStatusTracking = () => {
  const { state } = useEnhancedAppContext();

  // Calculate comprehensive stats
  const stats = {
    totalJobsheets: state.jobsheets.length,
    pendingJobsheets: state.jobsheets.filter((j) => j.status === "pending")
      .length,
    inProgressJobsheets: state.jobsheets.filter(
      (j) => j.status === "in-progress"
    ).length,
    completedJobsheets: state.jobsheets.filter((j) => j.status === "completed")
      .length,
    deliveredJobsheets: state.jobsheets.filter((j) => j.status === "delivered")
      .length,

    totalServices: state.services.length,
    pendingServices: state.services.filter((s) => s.status === "pending")
      .length,
    inProgressServices: state.services.filter((s) => s.status === "in-progress")
      .length,
    completedServices: state.services.filter((s) => s.status === "completed")
      .length,

    totalInvoices: state.invoices.length,
    draftInvoices: state.invoices.filter((i) => i.status === "draft").length,
    sentInvoices: state.invoices.filter((i) => i.status === "sent").length,
    paidInvoices: state.invoices.filter((i) => i.status === "paid").length,
    overdueInvoices: state.invoices.filter((i) => i.status === "overdue")
      .length,
  };

  // Calculate progress percentages
  const jobsheetProgress =
    stats.totalJobsheets > 0
      ? ((stats.completedJobsheets + stats.deliveredJobsheets) /
          stats.totalJobsheets) *
        100
      : 0;

  const serviceProgress =
    stats.totalServices > 0
      ? (stats.completedServices / stats.totalServices) * 100
      : 0;

  const invoiceProgress =
    stats.totalInvoices > 0
      ? (stats.paidInvoices / stats.totalInvoices) * 100
      : 0;

  // Calculate revenue metrics
  const totalRevenue = state.invoices
    .filter((i) => i.status === "paid")
    .reduce((sum, i) => sum + i.total, 0);

  const pendingRevenue = state.invoices
    .filter((i) => ["draft", "sent"].includes(i.status))
    .reduce((sum, i) => sum + i.total, 0);

  const overdueRevenue = state.invoices
    .filter((i) => i.status === "overdue")
    .reduce((sum, i) => sum + i.total, 0);

  const WorkflowCard = ({
    title,
    icon: Icon,
    total,
    progress,
    statusBreakdown,
  }: {
    title: string;
    icon: React.ComponentType<{ className?: string }>;
    total: number;
    progress: number;
    statusBreakdown: { label: string; count: number; color: string }[];
  }) => (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-sm">
          <Icon className="h-4 w-4" />
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-2xl font-bold">{total}</span>
          <span className="text-sm text-gray-500">
            {progress.toFixed(1)}% complete
          </span>
        </div>
        <Progress value={progress} className="h-2" />
        <div className="flex flex-wrap gap-1">
          {statusBreakdown.map((status) => (
            <Badge
              key={status.label}
              variant="outline"
              className={`text-xs ${status.color}`}
            >
              {status.label}: {status.count}
            </Badge>
          ))}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <WorkflowCard
          title="Jobsheet Workflow"
          icon={Package}
          total={stats.totalJobsheets}
          progress={jobsheetProgress}
          statusBreakdown={[
            {
              label: "Pending",
              count: stats.pendingJobsheets,
              color: "text-orange-600",
            },
            {
              label: "In Progress",
              count: stats.inProgressJobsheets,
              color: "text-blue-600",
            },
            {
              label: "Completed",
              count: stats.completedJobsheets,
              color: "text-green-600",
            },
            {
              label: "Delivered",
              count: stats.deliveredJobsheets,
              color: "text-gray-600",
            },
          ]}
        />

        <WorkflowCard
          title="Service Workflow"
          icon={Clock}
          total={stats.totalServices}
          progress={serviceProgress}
          statusBreakdown={[
            {
              label: "Pending",
              count: stats.pendingServices,
              color: "text-orange-600",
            },
            {
              label: "In Progress",
              count: stats.inProgressServices,
              color: "text-blue-600",
            },
            {
              label: "Completed",
              count: stats.completedServices,
              color: "text-green-600",
            },
          ]}
        />

        <WorkflowCard
          title="Invoice Workflow"
          icon={CheckCircle}
          total={stats.totalInvoices}
          progress={invoiceProgress}
          statusBreakdown={[
            {
              label: "Draft",
              count: stats.draftInvoices,
              color: "text-gray-600",
            },
            {
              label: "Sent",
              count: stats.sentInvoices,
              color: "text-blue-600",
            },
            {
              label: "Paid",
              count: stats.paidInvoices,
              color: "text-green-600",
            },
            {
              label: "Overdue",
              count: stats.overdueInvoices,
              color: "text-red-600",
            },
          ]}
        />
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5" />
            Revenue Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                ${totalRevenue.toFixed(2)}
              </div>
              <div className="text-sm text-gray-500">Total Revenue</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                ${pendingRevenue.toFixed(2)}
              </div>
              <div className="text-sm text-gray-500">Pending Revenue</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                ${overdueRevenue.toFixed(2)}
              </div>
              <div className="text-sm text-gray-500">Overdue Revenue</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
