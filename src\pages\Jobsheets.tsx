import React, { useState } from "react";
import { useEnhancedAppContext } from "@/context";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Plus,
  Search,
  ClipboardList,
  Calendar,
  User,
  Wrench,
  Edit,
  Eye,
  Download,
} from "lucide-react";
import { CreateJobsheetDialog } from "@/components/CreateJobsheetDialog";
import { EditJobsheetDialog } from "@/components/EditJobsheetDialog";
import { ViewJobsheetDialog } from "@/components/ViewJobsheetDialog";
import { StatusTransition } from "@/components/StatusTransition";
import { BreadcrumbNav } from "@/components/BreadcrumbNav";
import { RelationshipNav } from "@/components/RelationshipNav";
import { Jobsheet } from "@/types";

const Jobsheets = () => {
  const { state, dispatch } = useEnhancedAppContext();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingJobsheet, setEditingJobsheet] = useState<Jobsheet | null>(null);
  const [viewingJobsheet, setViewingJobsheet] = useState<Jobsheet | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const filteredJobsheets = state.jobsheets.filter(
    (jobsheet) =>
      jobsheet.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      jobsheet.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      jobsheet.deviceType.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalJobsheets = state.jobsheets.length;
  const pendingJobsheets = state.jobsheets.filter(
    (j) => j.status === "pending"
  ).length;
  const inProgressJobsheets = state.jobsheets.filter(
    (j) => j.status === "in-progress"
  ).length;
  const readyForDelivery = state.jobsheets.filter(
    (j) => j.status === "completed"
  ).length;

  const handleStatusChange = (jobsheetId: string, newStatus: string) => {
    const jobsheet = state.jobsheets.find((j) => j.id === jobsheetId);
    if (jobsheet) {
      const updatedJobsheet = {
        ...jobsheet,
        status: newStatus as Jobsheet["status"],
        updatedAt: new Date().toISOString(),
      };
      dispatch({ type: "UPDATE_JOBSHEET", payload: updatedJobsheet });
    }
  };

  const handleDownloadJobsheet = (jobsheet: Jobsheet) => {
    const content = `
Jobsheet Report
===============
Jobsheet ID: ${jobsheet.id}
Customer: ${jobsheet.customerName}
Device: ${jobsheet.deviceType} ${jobsheet.brand} ${jobsheet.model}
Serial Number: ${jobsheet.serialNumber || "Not provided"}
Reported Issues: ${jobsheet.reportedIssues}
Physical Condition: ${jobsheet.physicalCondition}
Accessories: ${jobsheet.accessoriesReceived}
Estimated Cost: $${jobsheet.estimatedCost}
Advance Payment: $${jobsheet.advancePayment}
Expected Delivery: ${new Date(jobsheet.expectedDelivery).toDateString()}
Technician: ${jobsheet.technicianAssigned || "Not assigned"}
Priority: ${jobsheet.priority}
Created: ${new Date(jobsheet.createdAt).toLocaleString()}
Status: ${jobsheet.status}
    `;

    const blob = new Blob([content], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `jobsheet-${jobsheet.id}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handlePrintJobsheet = (jobsheet: Jobsheet) => {
    const printWindow = window.open("", "_blank");
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head><title>Jobsheet - ${jobsheet.id}</title></head>
          <body>
            <h1>Jobsheet Report</h1>
            <p><strong>Jobsheet ID:</strong> ${jobsheet.id}</p>
            <p><strong>Customer:</strong> ${jobsheet.customerName}</p>
            <p><strong>Device:</strong> ${jobsheet.deviceType} ${
        jobsheet.brand
      } ${jobsheet.model}</p>
            <p><strong>Serial Number:</strong> ${
              jobsheet.serialNumber || "Not provided"
            }</p>
            <p><strong>Reported Issues:</strong> ${jobsheet.reportedIssues}</p>
            <p><strong>Physical Condition:</strong> ${
              jobsheet.physicalCondition
            }</p>
            <p><strong>Accessories:</strong> ${jobsheet.accessoriesReceived}</p>
            <p><strong>Estimated Cost:</strong> $${jobsheet.estimatedCost}</p>
            <p><strong>Advance Payment:</strong> $${jobsheet.advancePayment}</p>
            <p><strong>Expected Delivery:</strong> ${new Date(
              jobsheet.expectedDelivery
            ).toDateString()}</p>
            <p><strong>Technician:</strong> ${
              jobsheet.technicianAssigned || "Not assigned"
            }</p>
            <p><strong>Priority:</strong> ${jobsheet.priority}</p>
            <p><strong>Created:</strong> ${new Date(
              jobsheet.createdAt
            ).toLocaleString()}</p>
            <p><strong>Status:</strong> ${jobsheet.status}</p>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex flex-col gap-4">
          <BreadcrumbNav />
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">
              Jobsheet Management
            </h1>
            <Button
              className="bg-orange-500 hover:bg-orange-600"
              onClick={() => setIsCreateDialogOpen(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              New Jobsheet
            </Button>
          </div>
        </div>
      </header>

      <div className="p-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Total Jobsheets
              </CardTitle>
              <ClipboardList className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalJobsheets}</div>
              <p className="text-xs text-muted-foreground mt-1">All time</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <Calendar className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-500">
                {pendingJobsheets}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Awaiting service
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">In Progress</CardTitle>
              <Wrench className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-500">
                {inProgressJobsheets}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Being worked on
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                Ready for Delivery
              </CardTitle>
              <User className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-500">
                {readyForDelivery}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Completed work
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Jobsheets Section */}
        <Card>
          <CardHeader>
            <CardTitle>Jobsheets</CardTitle>
            <p className="text-sm text-muted-foreground">
              Manage device intake and service assignments
            </p>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search jobsheets..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Table Header */}
            <div className="grid grid-cols-8 gap-4 py-3 px-4 bg-gray-50 rounded-t-lg text-sm font-medium text-gray-700">
              <div>ID</div>
              <div>Customer</div>
              <div>Device</div>
              <div>Issue</div>
              <div>Status</div>
              <div>Created</div>
              <div>Delivery Date</div>
              <div>Actions</div>
            </div>

            {/* Jobsheet List or Empty State */}
            {filteredJobsheets.length === 0 ? (
              <div className="border border-t-0 rounded-b-lg p-12 text-center">
                <ClipboardList className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchTerm ? "No jobsheets found" : "No jobsheets yet"}
                </h3>
                <p className="text-gray-500 mb-4">
                  {searchTerm
                    ? "Try adjusting your search criteria"
                    : "Create your first jobsheet to start managing device intake"}
                </p>
                {!searchTerm && (
                  <Button
                    className="bg-orange-500 hover:bg-orange-600"
                    onClick={() => setIsCreateDialogOpen(true)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Create Jobsheet
                  </Button>
                )}
              </div>
            ) : (
              <div className="border border-t-0 rounded-b-lg">
                {filteredJobsheets.map((jobsheet) => (
                  <div
                    key={jobsheet.id}
                    className="grid grid-cols-8 gap-4 py-3 px-4 border-b last:border-b-0"
                  >
                    <div className="font-medium">{jobsheet.id}</div>
                    <div>
                      <div>{jobsheet.customerName}</div>
                      <RelationshipNav
                        customerId={jobsheet.customerId}
                        className="mt-1"
                      />
                    </div>
                    <div>
                      {jobsheet.deviceType} {jobsheet.brand}
                    </div>
                    <div className="truncate">{jobsheet.reportedIssues}</div>
                    <div>
                      <StatusTransition
                        currentStatus={jobsheet.status}
                        entityType="jobsheet"
                        onStatusChange={(newStatus) =>
                          handleStatusChange(jobsheet.id, newStatus)
                        }
                      />
                    </div>
                    <div>
                      {new Date(jobsheet.createdAt).toLocaleDateString()}
                    </div>
                    <div>
                      {new Date(jobsheet.expectedDelivery).toLocaleDateString()}
                    </div>
                    <div className="flex gap-1">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setEditingJobsheet(jobsheet)}
                        title="Edit Jobsheet"
                      >
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setViewingJobsheet(jobsheet)}
                        title="View Jobsheet"
                      >
                        <Eye className="h-3 w-3" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownloadJobsheet(jobsheet)}
                        title="Download/Print"
                      >
                        <Download className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <CreateJobsheetDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />

      {editingJobsheet && (
        <EditJobsheetDialog
          jobsheet={editingJobsheet}
          open={!!editingJobsheet}
          onOpenChange={(open) => !open && setEditingJobsheet(null)}
        />
      )}

      {viewingJobsheet && (
        <ViewJobsheetDialog
          jobsheet={viewingJobsheet}
          open={!!viewingJobsheet}
          onOpenChange={(open) => !open && setViewingJobsheet(null)}
        />
      )}
    </div>
  );
};

export default Jobsheets;
