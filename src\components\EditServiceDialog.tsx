import React, { useState } from "react";
import { useEnhancedAppContext } from "@/context";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Service, Comment } from "@/types";
import { EditHistoryPanel } from "./EditHistoryPanel";

interface EditServiceDialogProps {
  service: Service;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const EditServiceDialog = ({
  service,
  open,
  onOpenChange,
}: EditServiceDialogProps) => {
  const { state, dispatch } = useEnhancedAppContext();
  const [comments, setComments] = useState<Comment[]>([]);

  const handleAddComment = (comment: Omit<Comment, "id" | "timestamp">) => {
    const newComment: Comment = {
      ...comment,
      id: `CMT-${Date.now()}-${Math.random()}`,
      timestamp: new Date().toISOString(),
    };
    setComments((prev) => [...prev, newComment]);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    const updateComment: Comment = {
      id: `CMT-${Date.now()}-update`,
      entityType: "service",
      entityId: service.id,
      content: "Service updated",
      author: "System",
      timestamp: new Date().toISOString(),
    };

    const updatedService: Service = {
      ...service,
      description: formData.get("description") as string,
      cost: Number(formData.get("cost")),
      status: formData.get("status") as "pending" | "in-progress" | "completed",
      technicianNotes: formData.get("technicianNotes") as string,
      updatedAt: new Date().toISOString(),
      comments: [updateComment, ...comments, ...service.comments],
    };

    dispatch({ type: "UPDATE_SERVICE", payload: updatedService });
    onOpenChange(false);
    setComments([]);
  };

  const jobsheet = state.jobsheets.find((j) => j.id === service.jobsheetId);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Edit Service - {service.id}</DialogTitle>
        </DialogHeader>

        <div className="flex h-[calc(70vh)]">
          <div className="flex-1 overflow-y-auto pr-4">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Service ID</Label>
                  <Input value={service.id} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Jobsheet ID</Label>
                  <Input
                    value={service.jobsheetId}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
              </div>

              <div>
                <Label>Customer</Label>
                <Input
                  value={jobsheet?.customerName || "Unknown"}
                  readOnly
                  className="bg-gray-50"
                />
              </div>

              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  name="description"
                  defaultValue={service.description}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="cost">Cost *</Label>
                  <Input
                    id="cost"
                    name="cost"
                    type="number"
                    step="0.01"
                    defaultValue={service.cost}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="status">Status *</Label>
                  <Select name="status" defaultValue={service.status}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">Pending</SelectItem>
                      <SelectItem value="in-progress">In Progress</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <Label htmlFor="technicianNotes">Technician Notes</Label>
                <Textarea
                  id="technicianNotes"
                  name="technicianNotes"
                  defaultValue={service.technicianNotes || ""}
                  placeholder="Add any technician notes..."
                />
              </div>

              <div className="flex gap-2">
                <Button type="submit" className="flex-1">
                  Update Service
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>

          <EditHistoryPanel
            entityType="service"
            entityId={service.id}
            comments={comments}
            onAddComment={handleAddComment}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
