import React, { useState } from "react";
import { useEnhancedAppContext } from "@/context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { formatCurrency, formatDate } from "@/utils/dateUtils";
import { Plus, Search, Download, Eye, Edit } from "lucide-react";
import { Invoice } from "@/types";
import { ViewInvoiceDialog } from "@/components/ViewInvoiceDialog";
import { EditInvoiceDialog } from "@/components/EditInvoiceDialog";
import { StatusTransition } from "@/components/StatusTransition";
import { BreadcrumbNav } from "@/components/BreadcrumbNav";
import { RelationshipNav } from "@/components/RelationshipNav";
import { CreateInvoiceDialog } from "@/components/CreateInvoiceDialog";

const Invoices = () => {
  const { state, dispatch } = useEnhancedAppContext();
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [viewingInvoice, setViewingInvoice] = useState<Invoice | null>(null);
  const [editingInvoice, setEditingInvoice] = useState<Invoice | null>(null);

  const filteredInvoices = state.invoices.filter((invoice) => {
    const matchesSearch =
      invoice.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === "all" || invoice.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const handleStatusChange = (invoiceId: string, newStatus: string) => {
    const invoice = state.invoices.find((i) => i.id === invoiceId);
    if (invoice) {
      const updatedInvoice = {
        ...invoice,
        status: newStatus as Invoice["status"],
        updatedAt: new Date().toISOString(),
      };
      dispatch({ type: "UPDATE_INVOICE", payload: updatedInvoice });
    }
  };

  const handleDownloadInvoice = (invoice: Invoice) => {
    const content = `
INVOICE
=======
Invoice ID: ${invoice.id}
Customer: ${invoice.customerName}
Phone: ${invoice.customerPhone}
Address: ${invoice.customerAddress}
Device: ${invoice.deviceInfo}
Issue Date: ${formatDate(invoice.issueDate)}
Due Date: ${formatDate(invoice.dueDate)}

Subtotal: ${formatCurrency(invoice.subtotal)}
Tax: ${formatCurrency(invoice.tax)}
Total: ${formatCurrency(invoice.total)}

Status: ${invoice.status}
    `;

    const blob = new Blob([content], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `invoice-${invoice.id}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex flex-col gap-4">
        <BreadcrumbNav />
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Invoices</h1>
            <p className="text-gray-600">Manage billing and payments</p>
          </div>
          <Button
            onClick={() => setIsCreateDialogOpen(true)}
            className="bg-orange-500 hover:bg-orange-600"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Invoice
          </Button>
        </div>
      </div>

      <div className="flex gap-4 mb-6">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search invoices..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-48">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="draft">Draft</SelectItem>
            <SelectItem value="sent">Sent</SelectItem>
            <SelectItem value="paid">Paid</SelectItem>
            <SelectItem value="overdue">Overdue</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="grid gap-4">
        {filteredInvoices.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <p className="text-gray-500">
                {searchTerm || statusFilter !== "all"
                  ? "No invoices found matching your criteria"
                  : "No invoices found"}
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredInvoices.map((invoice) => (
            <Card key={invoice.id}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-4 mb-2">
                      <h3 className="font-semibold text-lg">{invoice.id}</h3>
                      <StatusTransition
                        currentStatus={invoice.status}
                        entityType="invoice"
                        onStatusChange={(newStatus) =>
                          handleStatusChange(invoice.id, newStatus)
                        }
                      />
                    </div>
                    <p className="text-gray-600 mb-1">
                      Customer: {invoice.customerName}
                    </p>
                    <p className="text-gray-600 mb-1">
                      Device: {invoice.deviceInfo}
                    </p>
                    <p className="text-gray-600 mb-1">
                      Issue Date: {formatDate(invoice.issueDate)}
                    </p>
                    <p className="text-gray-600 mb-2">
                      Due Date: {formatDate(invoice.dueDate)}
                    </p>
                    <RelationshipNav
                      customerId={invoice.customerId}
                      jobsheetId={invoice.jobsheetId}
                    />
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-orange-600">
                      {formatCurrency(invoice.total)}
                    </p>
                    <p className="text-gray-500">Total Amount</p>
                  </div>
                  <div className="flex items-center gap-2 ml-4">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setEditingInvoice(invoice)}
                      title="Edit Invoice"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setViewingInvoice(invoice)}
                      title="View Invoice"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDownloadInvoice(invoice)}
                      title="Download"
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      <CreateInvoiceDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />

      {viewingInvoice && (
        <ViewInvoiceDialog
          invoice={viewingInvoice}
          open={!!viewingInvoice}
          onOpenChange={(open) => !open && setViewingInvoice(null)}
        />
      )}

      {editingInvoice && (
        <EditInvoiceDialog
          invoice={editingInvoice}
          open={!!editingInvoice}
          onOpenChange={(open) => !open && setEditingInvoice(null)}
        />
      )}
    </div>
  );
};

export default Invoices;
