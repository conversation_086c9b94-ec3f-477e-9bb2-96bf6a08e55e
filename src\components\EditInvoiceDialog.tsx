import React, { useState } from "react";
import { useEnhancedAppContext } from "@/context";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Invoice, Comment } from "@/types";
import { EditHistoryPanel } from "./EditHistoryPanel";

interface EditInvoiceDialogProps {
  invoice: Invoice;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const EditInvoiceDialog = ({
  invoice,
  open,
  onOpenChange,
}: EditInvoiceDialogProps) => {
  const { state, dispatch } = useEnhancedAppContext();
  const [comments, setComments] = useState<Comment[]>([]);

  const handleAddComment = (comment: Omit<Comment, "id" | "timestamp">) => {
    const newComment: Comment = {
      ...comment,
      id: `CMT-${Date.now()}-${Math.random()}`,
      timestamp: new Date().toISOString(),
    };
    setComments((prev) => [...prev, newComment]);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    const updateComment: Comment = {
      id: `CMT-${Date.now()}-update`,
      entityType: "invoice",
      entityId: invoice.id,
      content: "Invoice updated",
      author: "System",
      timestamp: new Date().toISOString(),
    };

    const subtotal = Number(formData.get("subtotal"));
    const tax = Number(formData.get("tax"));

    const updatedInvoice: Invoice = {
      ...invoice,
      subtotal,
      tax,
      total: subtotal + tax,
      status: formData.get("status") as "draft" | "sent" | "paid" | "overdue",
      issueDate: formData.get("issueDate") as string,
      dueDate: formData.get("dueDate") as string,
      updatedAt: new Date().toISOString(),
      comments: [updateComment, ...comments, ...invoice.comments],
    };

    dispatch({ type: "UPDATE_INVOICE", payload: updatedInvoice });
    onOpenChange(false);
    setComments([]);
  };

  const services = state.services.filter((s) =>
    invoice.serviceIds.includes(s.id)
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Edit Invoice - {invoice.id}</DialogTitle>
        </DialogHeader>

        <div className="flex h-[calc(70vh)]">
          <div className="flex-1 overflow-y-auto pr-4">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Invoice ID</Label>
                  <Input value={invoice.id} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label htmlFor="status">Status *</Label>
                  <Select name="status" defaultValue={invoice.status}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="sent">Sent</SelectItem>
                      <SelectItem value="paid">Paid</SelectItem>
                      <SelectItem value="overdue">Overdue</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Customer Name</Label>
                  <Input
                    value={invoice.customerName}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label>Jobsheet ID</Label>
                  <Input
                    value={invoice.jobsheetId}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="issueDate">Issue Date *</Label>
                  <Input
                    id="issueDate"
                    name="issueDate"
                    type="date"
                    defaultValue={invoice.issueDate}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="dueDate">Due Date *</Label>
                  <Input
                    id="dueDate"
                    name="dueDate"
                    type="date"
                    defaultValue={invoice.dueDate}
                    required
                  />
                </div>
              </div>

              <div>
                <Label>Services</Label>
                <div className="mt-2 space-y-2">
                  {services.map((service) => (
                    <div
                      key={service.id}
                      className="flex justify-between p-2 bg-gray-50 rounded"
                    >
                      <span>{service.description}</span>
                      <span>${service.cost.toFixed(2)}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="subtotal">Subtotal *</Label>
                  <Input
                    id="subtotal"
                    name="subtotal"
                    type="number"
                    step="0.01"
                    defaultValue={invoice.subtotal}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="tax">Tax *</Label>
                  <Input
                    id="tax"
                    name="tax"
                    type="number"
                    step="0.01"
                    defaultValue={invoice.tax}
                    required
                  />
                </div>
              </div>

              <div>
                <Label>Total (Calculated)</Label>
                <Input
                  value={`$${(invoice.subtotal + invoice.tax).toFixed(2)}`}
                  readOnly
                  className="bg-gray-50 font-bold"
                />
              </div>

              <div className="flex gap-2">
                <Button type="submit" className="flex-1">
                  Update Invoice
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>

          <EditHistoryPanel
            entityType="invoice"
            entityId={invoice.id}
            comments={comments}
            onAddComment={handleAddComment}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
