import { createContext } from "react";
import { AppState, AppAction } from "./types";
import { DataSynchronizer } from "@/utils/dataSynchronizer";

export const EnhancedAppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  performDataSync: () => Promise<void>;
  generateSyncReport: () => ReturnType<
    typeof DataSynchronizer.generateSyncReport
  >;
} | null>(null);
