import React, { useState } from "react";
import { useEnhancedAppContext } from "@/context";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Plus,
  Search,
  Settings,
  AlertTriangle,
  Clock,
  CheckCircle,
  Edit,
  Eye,
  FileText,
  Download,
} from "lucide-react";
import { CreateJobsheetDialog } from "@/components/CreateJobsheetDialog";
import { EditJobsheetDialog } from "@/components/EditJobsheetDialog";
import { ViewJobsheetDialog } from "@/components/ViewJobsheetDialog";
import { CreateInvoiceFromServiceDialog } from "@/components/CreateInvoiceFromServiceDialog";
import { StatusTransition } from "@/components/StatusTransition";
import { BreadcrumbNav } from "@/components/BreadcrumbNav";
import { RelationshipNav } from "@/components/RelationshipNav";
import { Service, Jobsheet } from "@/types";

const Services = () => {
  const { state, dispatch } = useEnhancedAppContext();
  const [isCreateJobsheetOpen, setIsCreateJobsheetOpen] = useState(false);
  const [editingJobsheet, setEditingJobsheet] = useState<Jobsheet | null>(null);
  const [viewingJobsheet, setViewingJobsheet] = useState<Jobsheet | null>(null);
  const [creatingInvoiceService, setCreatingInvoiceService] =
    useState<Service | null>(null);
  const [searchTerm, setSearchTerm] = useState("");

  const filteredServices = state.services.filter((service) => {
    const jobsheet = state.jobsheets.find((j) => j.id === service.jobsheetId);
    return (
      service.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      jobsheet?.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      false
    );
  });

  const totalServices = state.services.length;
  const pendingServices = state.services.filter(
    (s) => s.status === "pending"
  ).length;
  const inProgressServices = state.services.filter(
    (s) => s.status === "in-progress"
  ).length;
  const completedServices = state.services.filter(
    (s) => s.status === "completed"
  ).length;

  const handleStatusChange = (serviceId: string, newStatus: string) => {
    const service = state.services.find((s) => s.id === serviceId);
    if (service) {
      const updatedService = {
        ...service,
        status: newStatus as Service["status"],
        updatedAt: new Date().toISOString(),
      };
      dispatch({ type: "UPDATE_SERVICE", payload: updatedService });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-orange-100 text-orange-800";
      case "in-progress":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const handleEditJobsheet = (service: Service) => {
    const jobsheet = state.jobsheets.find((j) => j.id === service.jobsheetId);
    if (jobsheet) {
      setEditingJobsheet(jobsheet);
    }
  };

  const handleViewJobsheet = (service: Service) => {
    const jobsheet = state.jobsheets.find((j) => j.id === service.jobsheetId);
    if (jobsheet) {
      setViewingJobsheet(jobsheet);
    }
  };

  const handleDownloadService = (service: Service) => {
    const jobsheet = state.jobsheets.find((j) => j.id === service.jobsheetId);
    const content = `
Service Report
==============
Service ID: ${service.id}
Jobsheet ID: ${service.jobsheetId}
Customer: ${jobsheet?.customerName || "Unknown"}
Description: ${service.description}
Status: ${service.status}
Cost: $${service.cost.toFixed(2)}
Created: ${new Date(service.createdAt).toLocaleString()}
Technician Notes: ${service.technicianNotes || "None"}
    `;

    const blob = new Blob([content], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `service-${service.id}.txt`;
    a.click();
    URL.revokeObjectURL(url);
  };

  const handlePrintService = (service: Service) => {
    const jobsheet = state.jobsheets.find((j) => j.id === service.jobsheetId);
    const printWindow = window.open("", "_blank");
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head><title>Service Report - ${service.id}</title></head>
          <body>
            <h1>Service Report</h1>
            <p><strong>Service ID:</strong> ${service.id}</p>
            <p><strong>Jobsheet ID:</strong> ${service.jobsheetId}</p>
            <p><strong>Customer:</strong> ${
              jobsheet?.customerName || "Unknown"
            }</p>
            <p><strong>Description:</strong> ${service.description}</p>
            <p><strong>Status:</strong> ${service.status}</p>
            <p><strong>Cost:</strong> $${service.cost.toFixed(2)}</p>
            <p><strong>Created:</strong> ${new Date(
              service.createdAt
            ).toLocaleString()}</p>
            <p><strong>Technician Notes:</strong> ${
              service.technicianNotes || "None"
            }</p>
          </body>
        </html>
      `);
      printWindow.document.close();
      printWindow.print();
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex flex-col gap-4">
          <BreadcrumbNav />
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">
              Service Management
            </h1>
            <Button
              className="bg-orange-500 hover:bg-orange-600"
              onClick={() => setIsCreateJobsheetOpen(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              New Service Request
            </Button>
          </div>
        </div>
      </header>

      <div className="p-6">
        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">
                All Services
              </CardTitle>
              <Settings className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalServices}</div>
              <p className="text-xs text-muted-foreground mt-1">
                Total service requests
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending</CardTitle>
              <AlertTriangle className="h-4 w-4 text-orange-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-500">
                {pendingServices}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Awaiting attention
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">In Progress</CardTitle>
              <Clock className="h-4 w-4 text-blue-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-500">
                {inProgressServices}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Currently being worked on
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Completed</CardTitle>
              <CheckCircle className="h-4 w-4 text-green-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-500">
                {completedServices}
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Successfully finished
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Service Requests Section */}
        <Card>
          <CardHeader>
            <CardTitle>Service Requests & Final Invoices</CardTitle>
            <p className="text-sm text-muted-foreground">
              Manage laptop repair services and finalize invoices
            </p>
          </CardHeader>
          <CardContent>
            <div className="mb-4 flex gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search services..."
                  className="pl-10"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
            </div>

            {/* Table Header */}
            <div className="grid grid-cols-9 gap-4 py-3 px-4 bg-gray-50 rounded-t-lg text-sm font-medium text-gray-700">
              <div>Service ID</div>
              <div>Jobsheet ID</div>
              <div>Customer</div>
              <div>Description</div>
              <div>Status</div>
              <div>Cost</div>
              <div>Created</div>
              <div>Relations</div>
              <div>Actions</div>
            </div>

            {/* Service List or Empty State */}
            {filteredServices.length === 0 ? (
              <div className="border border-t-0 rounded-b-lg p-12 text-center">
                <Settings className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  {searchTerm ? "No services found" : "No service requests yet"}
                </h3>
                <p className="text-gray-500 mb-4">
                  {searchTerm
                    ? "Try adjusting your search criteria"
                    : "Create your first service request to start managing repairs"}
                </p>
                {!searchTerm && (
                  <Button
                    className="bg-orange-500 hover:bg-orange-600"
                    onClick={() => setIsCreateJobsheetOpen(true)}
                  >
                    <Plus className="h-4 w-4 mr-2" />
                    Add Service Request
                  </Button>
                )}
              </div>
            ) : (
              <div className="border border-t-0 rounded-b-lg">
                {filteredServices.map((service) => {
                  const jobsheet = state.jobsheets.find(
                    (j) => j.id === service.jobsheetId
                  );
                  return (
                    <div
                      key={service.id}
                      className="grid grid-cols-9 gap-4 py-3 px-4 border-b last:border-b-0"
                    >
                      <div className="font-medium">{service.id}</div>
                      <div>{service.jobsheetId}</div>
                      <div>{jobsheet?.customerName || "Unknown"}</div>
                      <div className="truncate">{service.description}</div>
                      <div>
                        <StatusTransition
                          currentStatus={service.status}
                          entityType="service"
                          onStatusChange={(newStatus) =>
                            handleStatusChange(service.id, newStatus)
                          }
                        />
                      </div>
                      <div>${service.cost.toFixed(2)}</div>
                      <div>
                        {new Date(service.createdAt).toLocaleDateString()}
                      </div>
                      <div>
                        <RelationshipNav
                          customerId={jobsheet?.customerId}
                          jobsheetId={service.jobsheetId}
                        />
                      </div>
                      <div className="flex gap-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditJobsheet(service)}
                          title="Edit Jobsheet"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewJobsheet(service)}
                          title="View Jobsheet"
                        >
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setCreatingInvoiceService(service)}
                          disabled={service.status !== "completed"}
                          title="Create Invoice"
                        >
                          <FileText className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDownloadService(service)}
                          title="Download/Print"
                        >
                          <Download className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      <CreateJobsheetDialog
        open={isCreateJobsheetOpen}
        onOpenChange={setIsCreateJobsheetOpen}
      />

      {editingJobsheet && (
        <EditJobsheetDialog
          jobsheet={editingJobsheet}
          open={!!editingJobsheet}
          onOpenChange={(open) => !open && setEditingJobsheet(null)}
        />
      )}

      {viewingJobsheet && (
        <ViewJobsheetDialog
          jobsheet={viewingJobsheet}
          open={!!viewingJobsheet}
          onOpenChange={(open) => !open && setViewingJobsheet(null)}
        />
      )}

      {creatingInvoiceService && (
        <CreateInvoiceFromServiceDialog
          service={creatingInvoiceService}
          open={!!creatingInvoiceService}
          onOpenChange={(open) => !open && setCreatingInvoiceService(null)}
        />
      )}
    </div>
  );
};

export default Services;
