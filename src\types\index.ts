export type JobSheetPriority = 'low' | 'medium' | 'high';
export type JobSheetStatus = 'pending' | 'in-progress' | 'completed' | 'delivered';
export type ServiceStatus = 'pending' | 'in-progress' | 'completed';
export type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue';
export type ExpenseCategory = 'equipment' | 'office' | 'utilities' | 'supplies' | 'other';
export type CommentEntityType = 'jobsheet' | 'service' | 'invoice' | 'expense' | 'customer';
export interface Customer {
  id?: string; 
  name: string;
  email: string;
  phone: number;
  alternatePhone?: number;
  address: string;
  state?: string;
  city?: string;
  pincode?: string;
  createdAt: string;
  updatedAt: string;
  comments: Comment[];
}

export interface Jobsheet {
  id: string;
  customerId: string;
  customerName: string;
  deviceType: string;
  brand: string;
  model: string;
  serialNumber?: string;
  devicePassword?: string;
  reportedIssues: string;
  physicalCondition: string;
  accessoriesReceived: string;
  estimatedCost: number;
  advancePayment: number;
  expectedDelivery: string;
  technicianAssigned?: string;
  priority: JobSheetPriority;
  warrantyTerms?: string;
  specialInstructions?: string;
  receivedBy: string;
  customerSignatureObtained: boolean;
  termsAccepted: boolean;
  status: JobSheetStatus;
  createdAt: string;
  updatedAt: string;
  comments: Comment[];
}

export interface Service {
  id: string;
  jobsheetId: string;
  description: string;
  cost: number;
  status: ServiceStatus;
  technicianNotes?: string;
  createdAt: string;
  updatedAt: string;
  comments: Comment[];
}

export interface Invoice {
  id: string;
  customerId: string;
  customerName: string;
  customerPhone: number;
  customerAddress: string;
  jobsheetId: string;
  serviceIds: string[];
  deviceInfo: string;
  subtotal: number;
  tax: number;
  total: number;
  status: InvoiceStatus;
  issueDate: string;
  dueDate: string;
  createdAt: string;
  updatedAt: string;
  comments: Comment[];
}

export interface Expense {
  id: string;
  description: string;
  amount: number;
  category: ExpenseCategory;
  date: string;
  linkedInvoiceId?: string;
  receiptAttached: boolean;
  createdAt: string;
  updatedAt: string;
  comments: Comment[];
}

export interface Comment {
  id: string;
  entityType: CommentEntityType;
  entityId: string;
  content: string;
  author: string;
  timestamp: string;
}

export interface DashboardStats {
  totalCustomers: number;
  activeJobsheets: number;
  pendingInvoices: number;
  monthlyRevenue: number;
  completedServices: number;
  overdueInvoices: number;
}
