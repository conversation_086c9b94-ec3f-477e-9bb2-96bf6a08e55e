import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useEnhancedAppContext } from "@/context";
import { AlertTriangle, CheckCircle, RefreshCw, FileText } from "lucide-react";

export const DataIntegrityChecker = () => {
  const { state, performDataSync, generateSyncReport } =
    useEnhancedAppContext();
  const [showReport, setShowReport] = useState(false);

  const syncReport = generateSyncReport();
  const hasIssues =
    syncReport.orphanedServices.length > 0 ||
    syncReport.misalignedStatuses.length > 0 ||
    syncReport.invoiceIssues.length > 0;

  const handleSync = async () => {
    await performDataSync();
    setShowReport(true);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Data Integrity Check
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {hasIssues ? (
              <AlertTriangle className="h-5 w-5 text-orange-500" />
            ) : (
              <CheckCircle className="h-5 w-5 text-green-500" />
            )}
            <span>{hasIssues ? "Issues detected" : "Data integrity good"}</span>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowReport(!showReport)}
            >
              {showReport ? "Hide" : "Show"} Report
            </Button>
            <Button
              size="sm"
              onClick={handleSync}
              disabled={state.syncInProgress}
              className="bg-orange-500 hover:bg-orange-600"
            >
              {state.syncInProgress ? (
                <RefreshCw className="h-4 w-4 animate-spin mr-2" />
              ) : null}
              Sync Data
            </Button>
          </div>
        </div>

        {showReport && (
          <div className="space-y-4">
            {syncReport.orphanedServices.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Orphaned Services:</strong>{" "}
                  {syncReport.orphanedServices.length} services without valid
                  jobsheets
                  <div className="mt-2 flex flex-wrap gap-1">
                    {syncReport.orphanedServices.slice(0, 5).map((service) => (
                      <Badge key={service.id} variant="destructive">
                        {service.id}
                      </Badge>
                    ))}
                    {syncReport.orphanedServices.length > 5 && (
                      <Badge variant="outline">
                        +{syncReport.orphanedServices.length - 5} more
                      </Badge>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {syncReport.misalignedStatuses.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Status Misalignments:</strong>{" "}
                  {syncReport.misalignedStatuses.length} jobsheet-service pairs
                  out of sync
                  <div className="mt-2 flex flex-wrap gap-1">
                    {syncReport.misalignedStatuses
                      .slice(0, 3)
                      .map(({ jobsheet, service }) => (
                        <Badge
                          key={`${jobsheet.id}-${service.id}`}
                          variant="secondary"
                        >
                          {jobsheet.id} ↔ {service.id}
                        </Badge>
                      ))}
                    {syncReport.misalignedStatuses.length > 3 && (
                      <Badge variant="outline">
                        +{syncReport.misalignedStatuses.length - 3} more
                      </Badge>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {syncReport.invoiceIssues.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  <strong>Invoice Issues:</strong>{" "}
                  {syncReport.invoiceIssues.length} invoices with problems
                  <div className="mt-2 space-y-2">
                    {syncReport.invoiceIssues
                      .slice(0, 3)
                      .map(({ invoice, issues }) => (
                        <div key={invoice.id} className="text-sm">
                          <Badge variant="destructive">{invoice.id}</Badge>
                          <span className="ml-2">{issues.join(", ")}</span>
                        </div>
                      ))}
                    {syncReport.invoiceIssues.length > 3 && (
                      <div className="text-sm text-gray-500">
                        +{syncReport.invoiceIssues.length - 3} more invoices
                        with issues
                      </div>
                    )}
                  </div>
                </AlertDescription>
              </Alert>
            )}

            {!hasIssues && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  All data integrity checks passed. Your database is consistent.
                </AlertDescription>
              </Alert>
            )}

            <div className="text-xs text-gray-500">
              Last sync:{" "}
              {state.lastSyncTime
                ? new Date(state.lastSyncTime).toLocaleString()
                : "Never"}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
