
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Download, BarChart3, Archive, Settings, Users, TrendingUp, CreditCard } from "lucide-react";
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer } from 'recharts';

const Reports = () => {
  const mockData = [
    { name: 'Laptops', value: 40, color: '#8b5cf6' },
    { name: 'Accessories', value: 30, color: '#ec4899' },
    { name: 'Parts', value: 20, color: '#06b6d4' },
    { name: 'Services', value: 10, color: '#f59e0b' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
            <p className="text-sm text-muted-foreground mt-1">
              Comprehensive business analytics and reporting
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline">
              <Calendar className="h-4 w-4 mr-2" />
              This Month
            </Button>
            <Button className="bg-orange-500 hover:bg-orange-600">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>
      </header>

      <div className="p-6">
        {/* Report Categories */}
        <div className="flex gap-2 mb-6">
          <Badge className="bg-orange-500 text-white">Inventory</Badge>
          <Badge variant="outline">Service</Badge>
          <Badge variant="outline">Customer</Badge>
          <Badge variant="outline">Profit & Loss</Badge>
          <Badge variant="outline">Expenses</Badge>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Inventory Value Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Inventory Value by Category</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-[300px]">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={mockData}
                      cx="50%"
                      cy="50%"
                      innerRadius={60}
                      outerRadius={120}
                      paddingAngle={5}
                      dataKey="value"
                    >
                      {mockData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </CardContent>
          </Card>

          {/* Stock Levels Overview */}
          <Card>
            <CardHeader>
              <CardTitle>Stock Levels Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total Items</span>
                  <span className="text-2xl font-bold">3</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total Value</span>
                  <span className="text-2xl font-bold">₹220,100</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium text-red-600">Low Stock Items</span>
                  <span className="text-2xl font-bold text-red-600">1</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Categories</span>
                  <span className="text-2xl font-bold">3</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Reports;
