
import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, CheckCircle, Clock, FileText } from 'lucide-react';

type JobsheetStatus = 'pending' | 'in-progress' | 'completed' | 'delivered';
type ServiceStatus = 'pending' | 'in-progress' | 'completed';
type InvoiceStatus = 'draft' | 'sent' | 'paid' | 'overdue';

interface StatusTransitionProps {
  currentStatus: JobsheetStatus | ServiceStatus | InvoiceStatus;
  entityType: 'jobsheet' | 'service' | 'invoice';
  onStatusChange: (newStatus: string) => void;
  disabled?: boolean;
}

interface StatusConfig {
  icon: React.ComponentType<{ className?: string }>;
  color: string;
  nextStates: string[];
}

const statusConfigs: {
  jobsheet: Record<JobsheetStatus, StatusConfig>;
  service: Record<ServiceStatus, StatusConfig>;
  invoice: Record<InvoiceStatus, StatusConfig>;
} = {
  jobsheet: {
    pending: { icon: AlertTriangle, color: 'bg-orange-100 text-orange-800', nextStates: ['in-progress'] },
    'in-progress': { icon: Clock, color: 'bg-blue-100 text-blue-800', nextStates: ['completed'] },
    completed: { icon: CheckCircle, color: 'bg-green-100 text-green-800', nextStates: ['delivered'] },
    delivered: { icon: CheckCircle, color: 'bg-gray-100 text-gray-800', nextStates: [] }
  },
  service: {
    pending: { icon: AlertTriangle, color: 'bg-orange-100 text-orange-800', nextStates: ['in-progress'] },
    'in-progress': { icon: Clock, color: 'bg-blue-100 text-blue-800', nextStates: ['completed'] },
    completed: { icon: CheckCircle, color: 'bg-green-100 text-green-800', nextStates: [] }
  },
  invoice: {
    draft: { icon: FileText, color: 'bg-gray-100 text-gray-800', nextStates: ['sent'] },
    sent: { icon: Clock, color: 'bg-blue-100 text-blue-800', nextStates: ['paid', 'overdue'] },
    paid: { icon: CheckCircle, color: 'bg-green-100 text-green-800', nextStates: [] },
    overdue: { icon: AlertTriangle, color: 'bg-red-100 text-red-800', nextStates: ['paid'] }
  }
};

export const StatusTransition = ({ 
  currentStatus, 
  entityType, 
  onStatusChange, 
  disabled = false 
}: StatusTransitionProps) => {
  // Type guard to ensure we have a valid configuration
  const getStatusConfig = (): StatusConfig | null => {
    const entityConfig = statusConfigs[entityType];
    if (!entityConfig) return null;
    
    const statusKey = currentStatus as keyof typeof entityConfig;
    return entityConfig[statusKey] || null;
  };

  // Helper function to get status config for any status within the entity type
  const getStatusConfigForStatus = (status: string): StatusConfig | null => {
    const entityConfig = statusConfigs[entityType];
    if (!entityConfig) return null;
    
    // Check if the status exists in the current entity's config
    if (status in entityConfig) {
      return entityConfig[status as keyof typeof entityConfig];
    }
    
    return null;
  };

  const config = getStatusConfig();
  
  // Fallback if no valid configuration found
  if (!config) {
    return (
      <Badge className="bg-gray-100 text-gray-800">
        <AlertTriangle className="h-3 w-3 mr-1" />
        Unknown Status
      </Badge>
    );
  }

  const Icon = config.icon;
  const allowedNextStates = config.nextStates;

  if (disabled || allowedNextStates.length === 0) {
    return (
      <Badge className={config.color}>
        <Icon className="h-3 w-3 mr-1" />
        {currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1).replace('-', ' ')}
      </Badge>
    );
  }

  return (
    <Select value={currentStatus} onValueChange={onStatusChange}>
      <SelectTrigger className="w-40">
        <SelectValue>
          <div className="flex items-center">
            <Icon className="h-3 w-3 mr-2" />
            {currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1).replace('-', ' ')}
          </div>
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <SelectItem value={currentStatus} disabled>
          <div className="flex items-center">
            <Icon className="h-3 w-3 mr-2" />
            {currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1).replace('-', ' ')} (Current)
          </div>
        </SelectItem>
        {allowedNextStates.map(status => {
          const nextStatusConfig = getStatusConfigForStatus(status);
          if (!nextStatusConfig) return null;
          
          const NextIcon = nextStatusConfig.icon;
          return (
            <SelectItem key={status} value={status}>
              <div className="flex items-center">
                <NextIcon className="h-3 w-3 mr-2" />
                {status.charAt(0).toUpperCase() + status.slice(1).replace('-', ' ')}
              </div>
            </SelectItem>
          );
        })}
      </SelectContent>
    </Select>
  );
};
