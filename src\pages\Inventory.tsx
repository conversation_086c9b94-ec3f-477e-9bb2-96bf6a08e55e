
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Plus, Search, Archive } from "lucide-react";

const Inventory = () => {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Inventory Management</h1>
          <Button className="bg-orange-500 hover:bg-orange-600">
            <Plus className="h-4 w-4 mr-2" />
            Add Item
          </Button>
        </div>
      </header>

      <div className="p-6">
        {/* Inventory Section */}
        <Card>
          <CardHeader>
            <CardTitle>Inventory Items</CardTitle>
            <p className="text-sm text-muted-foreground">
              Manage laptop parts and accessories inventory
            </p>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search inventory..."
                  className="pl-10"
                />
              </div>
            </div>

            {/* Empty State */}
            <div className="border rounded-lg p-12 text-center">
              <Archive className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No inventory items yet</h3>
              <p className="text-gray-500 mb-4">
                Add your first inventory item to start tracking stock
              </p>
              <Button className="bg-orange-500 hover:bg-orange-600">
                <Plus className="h-4 w-4 mr-2" />
                Add Item
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default Inventory;
