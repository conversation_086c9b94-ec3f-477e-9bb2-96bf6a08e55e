import React, { useState } from "react";
import { useEnhancedAppContext } from "@/context";
import {
  <PERSON>alog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Jobsheet, Comment } from "@/types";
import { EditHistoryPanel } from "./EditHistoryPanel";

interface EditJobsheetDialogProps {
  jobsheet: Jobsheet;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const EditJobsheetDialog = ({
  jobsheet,
  open,
  onOpenChange,
}: EditJobsheetDialogProps) => {
  const { dispatch } = useEnhancedAppContext();
  const [comments, setComments] = useState<Comment[]>([]);

  const handleAddComment = (comment: Omit<Comment, "id" | "timestamp">) => {
    const newComment: Comment = {
      ...comment,
      id: `CMT-${Date.now()}-${Math.random()}`,
      timestamp: new Date().toISOString(),
    };
    setComments((prev) => [...prev, newComment]);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    const updateComment: Comment = {
      id: `CMT-${Date.now()}-update`,
      entityType: "jobsheet",
      entityId: jobsheet.id,
      content: "Jobsheet updated",
      author: "System",
      timestamp: new Date().toISOString(),
    };

    const updatedJobsheet: Jobsheet = {
      ...jobsheet,
      deviceType: formData.get("deviceType") as string,
      brand: formData.get("brand") as string,
      model: formData.get("model") as string,
      serialNumber: (formData.get("serialNumber") as string) || undefined,
      devicePassword: (formData.get("devicePassword") as string) || undefined,
      reportedIssues: formData.get("reportedIssues") as string,
      physicalCondition: formData.get("physicalCondition") as string,
      accessoriesReceived: formData.get("accessoriesReceived") as string,
      estimatedCost: Number(formData.get("estimatedCost")),
      advancePayment: Number(formData.get("advancePayment")),
      expectedDelivery: formData.get("expectedDelivery") as string,
      technicianAssigned:
        (formData.get("technicianAssigned") as string) || undefined,
      priority: formData.get("priority") as "low" | "medium" | "high",
      status: formData.get("status") as
        | "pending"
        | "in-progress"
        | "completed"
        | "delivered",
      warrantyTerms: (formData.get("warrantyTerms") as string) || undefined,
      specialInstructions:
        (formData.get("specialInstructions") as string) || undefined,
      updatedAt: new Date().toISOString(),
      comments: [updateComment, ...comments, ...jobsheet.comments],
    };

    dispatch({ type: "UPDATE_JOBSHEET", payload: updatedJobsheet });
    onOpenChange(false);
    setComments([]);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Edit Jobsheet - {jobsheet.id}</DialogTitle>
        </DialogHeader>

        <div className="flex h-[calc(90vh-120px)]">
          <div className="flex-1 overflow-y-auto pr-4">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Customer Information</h3>
                <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <Label>Customer Name</Label>
                    <Input
                      value={jobsheet.customerName}
                      readOnly
                      className="bg-white"
                    />
                  </div>
                  <div>
                    <Label>Jobsheet ID</Label>
                    <Input value={jobsheet.id} readOnly className="bg-white" />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Device Information</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="deviceType">Device Type *</Label>
                    <Input
                      id="deviceType"
                      name="deviceType"
                      defaultValue={jobsheet.deviceType}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="brand">Brand *</Label>
                    <Input
                      id="brand"
                      name="brand"
                      defaultValue={jobsheet.brand}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="model">Model *</Label>
                    <Input
                      id="model"
                      name="model"
                      defaultValue={jobsheet.model}
                      required
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="serialNumber">Serial Number</Label>
                    <Input
                      id="serialNumber"
                      name="serialNumber"
                      defaultValue={jobsheet.serialNumber || ""}
                    />
                  </div>
                  <div>
                    <Label htmlFor="devicePassword">Device Password</Label>
                    <Input
                      id="devicePassword"
                      name="devicePassword"
                      defaultValue={jobsheet.devicePassword || ""}
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Service Details</h3>
                <div>
                  <Label htmlFor="reportedIssues">Reported Issues *</Label>
                  <Textarea
                    id="reportedIssues"
                    name="reportedIssues"
                    defaultValue={jobsheet.reportedIssues}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="physicalCondition">
                    Physical Condition *
                  </Label>
                  <Textarea
                    id="physicalCondition"
                    name="physicalCondition"
                    defaultValue={jobsheet.physicalCondition}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="accessoriesReceived">
                    Accessories Received *
                  </Label>
                  <Textarea
                    id="accessoriesReceived"
                    name="accessoriesReceived"
                    defaultValue={jobsheet.accessoriesReceived}
                    required
                  />
                </div>
                <div className="grid grid-cols-4 gap-4">
                  <div>
                    <Label htmlFor="estimatedCost">Estimated Cost</Label>
                    <Input
                      id="estimatedCost"
                      name="estimatedCost"
                      type="number"
                      step="0.01"
                      defaultValue={jobsheet.estimatedCost}
                    />
                  </div>
                  <div>
                    <Label htmlFor="advancePayment">Advance Payment</Label>
                    <Input
                      id="advancePayment"
                      name="advancePayment"
                      type="number"
                      step="0.01"
                      defaultValue={jobsheet.advancePayment}
                    />
                  </div>
                  <div>
                    <Label htmlFor="expectedDelivery">Expected Delivery</Label>
                    <Input
                      id="expectedDelivery"
                      name="expectedDelivery"
                      type="date"
                      defaultValue={jobsheet.expectedDelivery}
                    />
                  </div>
                  <div>
                    <Label htmlFor="status">Status *</Label>
                    <Select name="status" defaultValue={jobsheet.status}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="in-progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="delivered">Delivered</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="technicianAssigned">
                      Technician Assigned
                    </Label>
                    <Input
                      id="technicianAssigned"
                      name="technicianAssigned"
                      defaultValue={jobsheet.technicianAssigned || ""}
                    />
                  </div>
                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Select name="priority" defaultValue={jobsheet.priority}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">
                  Additional Information
                </h3>
                <div>
                  <Label htmlFor="warrantyTerms">Warranty Terms</Label>
                  <Textarea
                    id="warrantyTerms"
                    name="warrantyTerms"
                    defaultValue={jobsheet.warrantyTerms || ""}
                  />
                </div>
                <div>
                  <Label htmlFor="specialInstructions">
                    Special Instructions
                  </Label>
                  <Textarea
                    id="specialInstructions"
                    name="specialInstructions"
                    defaultValue={jobsheet.specialInstructions || ""}
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <Button type="submit" className="flex-1">
                  Update Jobsheet
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>

          <EditHistoryPanel
            entityType="jobsheet"
            entityId={jobsheet.id}
            comments={comments}
            onAddComment={handleAddComment}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
