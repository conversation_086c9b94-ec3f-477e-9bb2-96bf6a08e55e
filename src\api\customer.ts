import axios from "./axios";
import { Comment, Customer as CustomerType } from "@/types";

export const getAllCustomers = async () => {
    try {
        const response = await axios.get(`/rest/v1/customer?limit=1000&offset=0`)
        if (response.data.length > 0){
            await response.data.map(async(customer : CustomerType) => {
                try{
                    const response = await axios.get(`/rest/v1/comment_map?select=*,comment(*),customer!inner(id)&customer.id=eq.${customer.id}&limit=1000&offset=0`)
                    customer.comments = response.data
                }catch(error){
                    console.error("[> Error] Loading Customer Comments", error);
                    customer.comments = [];
                }
            })
        }
        return response.data;
    } catch (error) {
        console.error("[> Error]", error);
        return [];
    }
}

export const PostAddCustomers = async ({
    name,
    email,
    phone,
    alternatePhone,
    address,
    state,
    city,
    pincode,
    comments
}) => {
    try {
        const response = await axios.post(`/rest/v1/customer`,{
            name,
            email,
            phone,
            alternatePhone,
            address,
            state,
            city,
            pincode,
        },{
            headers: {
                "Prefer": "return=representation"
            }
        })
        console.log("data", response.data);
        if (comments && comments.length > 0){
            response.data.comments=[]
            await comments.map(async (comments: Comment) => {
                try{
                    const commentsData = await axios.post(`/rest/v1/comment`, comments)
                    response.data.comments.push(commentsData.data);
                }catch(error){
                    console.error("[> Error] Adding Customer Comments", error);
                }
            })
        }
        return response.data;
    } catch (error) {
        console.error("[> Error]", error);
        return null;
    }
}

const Customer  = {
    getAll : getAllCustomers,
    add : PostAddCustomers,
}

//Customer Info
export default Customer;
