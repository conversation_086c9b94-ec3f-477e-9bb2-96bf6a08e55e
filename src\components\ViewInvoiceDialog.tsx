import React from "react";
import { useEnhancedAppContext } from "@/context";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { formatCurrency, formatDate } from "@/utils/dateUtils";
import { Invoice } from "@/types";
import { EditHistoryPanel } from "./EditHistoryPanel";

interface ViewInvoiceDialogProps {
  invoice: Invoice;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ViewInvoiceDialog = ({
  invoice,
  open,
  onOpenChange,
}: ViewInvoiceDialogProps) => {
  const { state } = useEnhancedAppContext();
  const services = state.services.filter((s) =>
    invoice.serviceIds.includes(s.id)
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft":
        return "bg-gray-100 text-gray-800";
      case "sent":
        return "bg-blue-100 text-blue-800";
      case "paid":
        return "bg-green-100 text-green-800";
      case "overdue":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>View Invoice - {invoice.id}</DialogTitle>
        </DialogHeader>

        <div className="flex h-[calc(70vh)]">
          <div className="flex-1 overflow-y-auto pr-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Invoice ID</Label>
                  <Input value={invoice.id} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Status</Label>
                  <div className="mt-2">
                    <Badge className={getStatusColor(invoice.status)}>
                      {invoice.status.charAt(0).toUpperCase() +
                        invoice.status.slice(1)}
                    </Badge>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Customer Name</Label>
                  <Input
                    value={invoice.customerName}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label>Jobsheet ID</Label>
                  <Input
                    value={invoice.jobsheetId}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Issue Date</Label>
                  <Input
                    value={formatDate(invoice.issueDate)}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label>Due Date</Label>
                  <Input
                    value={formatDate(invoice.dueDate)}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
              </div>

              <div>
                <Label>Services</Label>
                <div className="mt-2 space-y-2">
                  {services.map((service) => (
                    <div
                      key={service.id}
                      className="flex justify-between p-2 bg-gray-50 rounded"
                    >
                      <span>{service.description}</span>
                      <span>${service.cost.toFixed(2)}</span>
                    </div>
                  ))}
                </div>
              </div>

              <div className="border-t pt-4">
                <div className="flex justify-between mb-2">
                  <span>Subtotal:</span>
                  <span>{formatCurrency(invoice.subtotal)}</span>
                </div>
                <div className="flex justify-between mb-2">
                  <span>Tax:</span>
                  <span>{formatCurrency(invoice.tax)}</span>
                </div>
                <div className="flex justify-between font-bold text-lg">
                  <span>Total:</span>
                  <span>{formatCurrency(invoice.total)}</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Created At</Label>
                  <Input
                    value={new Date(invoice.createdAt).toLocaleString()}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label>Updated At</Label>
                  <Input
                    value={new Date(invoice.updatedAt).toLocaleString()}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
              </div>

              <Button onClick={() => onOpenChange(false)} className="w-full">
                Close
              </Button>
            </div>
          </div>

          <EditHistoryPanel
            entityType="invoice"
            entityId={invoice.id}
            comments={invoice.comments || []}
            onAddComment={() => {}} // Read-only mode
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
