
import { format, addDays } from 'date-fns';

export const generateDeliveryDate = (daysToAdd: number = 3): string => {
  const deliveryDate = addDays(new Date(), daysToAdd);
  return format(deliveryDate, 'yyyy-MM-dd');
};

export const formatDate = (date: string | Date): string => {
  return format(new Date(date), 'MMM dd, yyyy');
};

export const formatDateTime = (date: string | Date): string => {
  return format(new Date(date), 'MMM dd, yyyy HH:mm');
};

export const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(amount);
};
