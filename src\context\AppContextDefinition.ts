import { createContext } from "react";
import { Customer, Jobsheet, Service, Invoice, Expense } from "@/types";

export interface AppState {
  customers: Customer[];
  jobsheets: Jobsheet[];
  services: Service[];
  invoices: Invoice[];
  expenses: Expense[];
}

export type AppAction =
  | { type: "ADD_CUSTOMER"; payload: Customer }
  | { type: "UPDATE_CUSTOMER"; payload: Customer }
  | { type: "ADD_JOBSHEET"; payload: Jobsheet }
  | { type: "UPDATE_JOBSHEET"; payload: Jobsheet }
  | { type: "ADD_SERVICE"; payload: Service }
  | { type: "UPDATE_SERVICE"; payload: Service }
  | { type: "ADD_INVOICE"; payload: Invoice }
  | { type: "UPDATE_INVOICE"; payload: Invoice }
  | { type: "ADD_EXPENSE"; payload: Expense }
  | { type: "UPDATE_EXPENSE"; payload: Expense }
  | { type: "LOAD_STATE"; payload: AppState };

export const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
} | null>(null);
