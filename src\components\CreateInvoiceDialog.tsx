import React, { useState } from "react";
import { useEnhancedAppContext } from "@/context";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Plus, Trash, CalendarIcon } from "lucide-react";
import { Invoice, Comment } from "@/types";
import { EditHistoryPanel } from "./EditHistoryPanel";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { cn } from "@/lib/utils";

interface CreateInvoiceDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface PartUsed {
  id: string;
  name: string;
  quantity: number;
}

interface AdditionalExpense {
  id: string;
  description: string;
  amount: number;
  category: string;
}

export const CreateInvoiceDialog = ({
  open,
  onOpenChange,
}: CreateInvoiceDialogProps) => {
  const { state, dispatch } = useEnhancedAppContext();
  const [comments, setComments] = useState<Comment[]>([]);

  // Form state
  const [customerName, setCustomerName] = useState("");
  const [gstNumber, setGstNumber] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [alternatePhone, setAlternatePhone] = useState("");
  const [address, setAddress] = useState("");
  const [state_, setState] = useState("");
  const [city, setCity] = useState("");
  const [pincode, setPincode] = useState("");
  const [deviceType, setDeviceType] = useState("");
  const [device, setDevice] = useState("");
  const [issueDescription, setIssueDescription] = useState("");
  const [estimatedAmount, setEstimatedAmount] = useState("");
  const [finalAmount, setFinalAmount] = useState("");
  const [status, setStatus] = useState("pending");
  const [billableWarranty, setBillableWarranty] = useState("");
  const [inspectionFee, setInspectionFee] = useState("500");
  const [expectedDeliveryDate, setExpectedDeliveryDate] = useState<Date>();
  const [addRemarks, setAddRemarks] = useState(false);
  const [partsUsed, setPartsUsed] = useState<PartUsed[]>([]);
  const [additionalExpenses, setAdditionalExpenses] = useState<
    AdditionalExpense[]
  >([]);
  const [invoiceDate, setInvoiceDate] = useState<Date>(new Date());

  const handleAddComment = (comment: Omit<Comment, "id" | "timestamp">) => {
    const newComment: Comment = {
      ...comment,
      id: `CMT-${Date.now()}-${Math.random()}`,
      timestamp: new Date().toISOString(),
    };
    setComments((prev) => [...prev, newComment]);
  };

  const addPartUsed = () => {
    setPartsUsed([
      ...partsUsed,
      { id: Date.now().toString(), name: "", quantity: 1 },
    ]);
  };

  const removePartUsed = (id: string) => {
    setPartsUsed(partsUsed.filter((part) => part.id !== id));
  };

  const updatePartUsed = (
    id: string,
    field: keyof PartUsed,
    value: string | number
  ) => {
    setPartsUsed(
      partsUsed.map((part) =>
        part.id === id ? { ...part, [field]: value } : part
      )
    );
  };

  const addAdditionalExpense = () => {
    setAdditionalExpenses([
      ...additionalExpenses,
      {
        id: Date.now().toString(),
        description: "",
        amount: 0,
        category: "Parts",
      },
    ]);
  };

  const removeAdditionalExpense = (id: string) => {
    setAdditionalExpenses(
      additionalExpenses.filter((expense) => expense.id !== id)
    );
  };

  const updateAdditionalExpense = (
    id: string,
    field: keyof AdditionalExpense,
    value: string | number
  ) => {
    setAdditionalExpenses(
      additionalExpenses.map((expense) =>
        expense.id === id ? { ...expense, [field]: value } : expense
      )
    );
  };

  const handleCreateInvoice = () => {
    const invoiceId = `INV-${Date.now()}`;
    const subtotal = parseFloat(finalAmount) || 0;
    const tax = subtotal * 0.1; // 10% tax
    const total = subtotal + tax;

    const creationComment: Comment = {
      id: `CMT-${Date.now()}-creation`,
      entityType: "invoice",
      entityId: invoiceId,
      content: "Invoice created with comprehensive details",
      author: "System",
      timestamp: new Date().toISOString(),
    };

    const newInvoice: Invoice = {
      id: invoiceId,
      customerId: `CUST-${Date.now()}`,
      customerName,
      customerPhone: phoneNumber,
      customerAddress: address,
      jobsheetId: `JS-${Date.now()}`,
      serviceIds: [],
      deviceInfo: `${deviceType} - ${device}`,
      subtotal,
      tax,
      total,
      status: status as Invoice["status"],
      issueDate: format(invoiceDate, "yyyy-MM-dd"),
      dueDate: expectedDeliveryDate
        ? format(expectedDeliveryDate, "yyyy-MM-dd")
        : format(new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), "yyyy-MM-dd"),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      comments: [creationComment, ...comments],
    };

    dispatch({ type: "ADD_INVOICE", payload: newInvoice });

    // Reset form
    setCustomerName("");
    setGstNumber("");
    setPhoneNumber("");
    setAlternatePhone("");
    setAddress("");
    setState("");
    setCity("");
    setPincode("");
    setDeviceType("");
    setDevice("");
    setIssueDescription("");
    setEstimatedAmount("");
    setFinalAmount("");
    setStatus("pending");
    setBillableWarranty("");
    setInspectionFee("500");
    setExpectedDeliveryDate(undefined);
    setAddRemarks(false);
    setPartsUsed([]);
    setAdditionalExpenses([]);
    setComments([]);
    setInvoiceDate(new Date());

    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[95vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Create New Invoice
          </DialogTitle>
        </DialogHeader>

        <div className="flex h-[calc(85vh)] gap-6">
          <div className="flex-1 overflow-y-auto pr-4 space-y-6">
            {/* Customer Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Customer Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="customerName">Customer Name *</Label>
                    <Input
                      id="customerName"
                      value={customerName}
                      onChange={(e) => setCustomerName(e.target.value)}
                      placeholder="Enter customer name"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="invoiceDate">Date</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full justify-start text-left font-normal mt-1",
                            !invoiceDate && "text-muted-foreground"
                          )}
                        >
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {invoiceDate
                            ? format(invoiceDate, "MM/dd/yyyy")
                            : "Select date"}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={invoiceDate}
                          onSelect={setInvoiceDate}
                          initialFocus
                          className="p-3 pointer-events-auto"
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="flex items-end">
                    <Button variant="outline" className="w-full">
                      💬 Add Comment
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="gstNumber">GST (Optional)</Label>
                    <Input
                      id="gstNumber"
                      value={gstNumber}
                      onChange={(e) => setGstNumber(e.target.value)}
                      placeholder="GST Number"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="phoneNumber">Phone Number</Label>
                    <Input
                      id="phoneNumber"
                      value={phoneNumber}
                      onChange={(e) => setPhoneNumber(e.target.value)}
                      placeholder="+91 98765 43210"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="alternatePhone">Alternate Phone</Label>
                    <Input
                      id="alternatePhone"
                      value={alternatePhone}
                      onChange={(e) => setAlternatePhone(e.target.value)}
                      placeholder="+91 98765 43210"
                      className="mt-1"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="address">Address</Label>
                  <Textarea
                    id="address"
                    value={address}
                    onChange={(e) => setAddress(e.target.value)}
                    placeholder="Complete address"
                    className="mt-1 min-h-[80px]"
                  />
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="state">State</Label>
                    <Select value={state_} onValueChange={setState}>
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select state" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="maharashtra">Maharashtra</SelectItem>
                        <SelectItem value="delhi">Delhi</SelectItem>
                        <SelectItem value="karnataka">Karnataka</SelectItem>
                        <SelectItem value="tamil-nadu">Tamil Nadu</SelectItem>
                        <SelectItem value="gujarat">Gujarat</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="city">City</Label>
                    <Input
                      id="city"
                      value={city}
                      onChange={(e) => setCity(e.target.value)}
                      placeholder="City"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="pincode">Pincode</Label>
                    <Input
                      id="pincode"
                      value={pincode}
                      onChange={(e) => setPincode(e.target.value)}
                      placeholder="123456"
                      className="mt-1"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Device Information */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Device Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="deviceType">Device Type</Label>
                    <Select value={deviceType} onValueChange={setDeviceType}>
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select device type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="laptop">Laptop</SelectItem>
                        <SelectItem value="desktop">Desktop</SelectItem>
                        <SelectItem value="mobile">Mobile</SelectItem>
                        <SelectItem value="tablet">Tablet</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="device">Device</Label>
                    <Input
                      id="device"
                      value={device}
                      onChange={(e) => setDevice(e.target.value)}
                      placeholder="e.g., MacBook Pro"
                      className="mt-1"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="issueDescription">Issue Description</Label>
                  <Textarea
                    id="issueDescription"
                    value={issueDescription}
                    onChange={(e) => setIssueDescription(e.target.value)}
                    placeholder="Describe the problem"
                    className="mt-1 min-h-[100px]"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="estimatedAmount">Estimated Amount</Label>
                    <Input
                      id="estimatedAmount"
                      value={estimatedAmount}
                      onChange={(e) => setEstimatedAmount(e.target.value)}
                      placeholder="₹0"
                      className="mt-1"
                    />
                  </div>
                  <div>
                    <Label htmlFor="finalAmount">Final Amount</Label>
                    <Input
                      id="finalAmount"
                      value={finalAmount}
                      onChange={(e) => setFinalAmount(e.target.value)}
                      placeholder="₹0"
                      className="mt-1"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Service Details */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Service Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="status">Status</Label>
                    <Select value={status} onValueChange={setStatus}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">Pending</SelectItem>
                        <SelectItem value="in-progress">In Progress</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="billableWarranty">Billable/Warranty</Label>
                    <Select
                      value={billableWarranty}
                      onValueChange={setBillableWarranty}
                    >
                      <SelectTrigger className="mt-1">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="billable">Billable</SelectItem>
                        <SelectItem value="warranty">Warranty</SelectItem>
                        <SelectItem value="free-service">
                          Free Service
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="inspectionFee">Inspection Fee</Label>
                    <Input
                      id="inspectionFee"
                      value={inspectionFee}
                      onChange={(e) => setInspectionFee(e.target.value)}
                      className="mt-1"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="expectedDelivery">
                    Expected Delivery Date
                  </Label>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "w-full justify-start text-left font-normal mt-1",
                          !expectedDeliveryDate && "text-muted-foreground"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {expectedDeliveryDate
                          ? format(expectedDeliveryDate, "MM/dd/yyyy")
                          : "Select delivery date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={expectedDeliveryDate}
                        onSelect={setExpectedDeliveryDate}
                        initialFocus
                        className="p-3 pointer-events-auto"
                      />
                    </PopoverContent>
                  </Popover>
                  <p className="text-sm text-gray-500 mt-1">
                    Automatically calculated as 3 days after bill date
                  </p>
                </div>

                <div className="flex items-center space-x-2">
                  <Switch
                    id="addRemarks"
                    checked={addRemarks}
                    onCheckedChange={setAddRemarks}
                  />
                  <Label htmlFor="addRemarks">Add Remarks</Label>
                </div>
              </CardContent>
            </Card>

            {/* Parts Used */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg">Parts Used</CardTitle>
                <Button
                  onClick={addPartUsed}
                  size="sm"
                  className="bg-orange-500 hover:bg-orange-600"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Part
                </Button>
              </CardHeader>
              <CardContent>
                {partsUsed.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">
                    No parts added yet
                  </p>
                ) : (
                  <div className="space-y-3">
                    {partsUsed.map((part) => (
                      <div
                        key={part.id}
                        className="flex items-center gap-3 p-3 border rounded-lg"
                      >
                        <Select
                          value={part.name}
                          onValueChange={(value) =>
                            updatePartUsed(part.id, "name", value)
                          }
                        >
                          <SelectTrigger className="flex-1">
                            <SelectValue placeholder="Select part" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="battery">Battery</SelectItem>
                            <SelectItem value="screen">Screen</SelectItem>
                            <SelectItem value="keyboard">Keyboard</SelectItem>
                            <SelectItem value="motherboard">
                              Motherboard
                            </SelectItem>
                            <SelectItem value="ram">RAM</SelectItem>
                            <SelectItem value="storage">Storage</SelectItem>
                          </SelectContent>
                        </Select>
                        <Input
                          type="number"
                          value={part.quantity}
                          onChange={(e) =>
                            updatePartUsed(
                              part.id,
                              "quantity",
                              parseInt(e.target.value) || 1
                            )
                          }
                          className="w-20"
                          min="1"
                        />
                        <Button
                          onClick={() => removePartUsed(part.id)}
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Additional Expenses */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg">
                  Additional Expenses (Will be added to Expense Tracking)
                </CardTitle>
                <Button
                  onClick={addAdditionalExpense}
                  size="sm"
                  className="bg-orange-500 hover:bg-orange-600"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Add Expense
                </Button>
              </CardHeader>
              <CardContent>
                {additionalExpenses.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">
                    No additional expenses added
                  </p>
                ) : (
                  <div className="space-y-3">
                    {additionalExpenses.map((expense) => (
                      <div
                        key={expense.id}
                        className="flex items-center gap-3 p-3 border rounded-lg"
                      >
                        <Input
                          value={expense.description}
                          onChange={(e) =>
                            updateAdditionalExpense(
                              expense.id,
                              "description",
                              e.target.value
                            )
                          }
                          placeholder="Description"
                          className="flex-1"
                        />
                        <Input
                          type="number"
                          value={expense.amount}
                          onChange={(e) =>
                            updateAdditionalExpense(
                              expense.id,
                              "amount",
                              parseFloat(e.target.value) || 0
                            )
                          }
                          placeholder="₹0"
                          className="w-24"
                        />
                        <Select
                          value={expense.category}
                          onValueChange={(value) =>
                            updateAdditionalExpense(
                              expense.id,
                              "category",
                              value
                            )
                          }
                        >
                          <SelectTrigger className="w-32">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="Parts">Parts</SelectItem>
                            <SelectItem value="Labor">Labor</SelectItem>
                            <SelectItem value="Transport">Transport</SelectItem>
                            <SelectItem value="Other">Other</SelectItem>
                          </SelectContent>
                        </Select>
                        <Button
                          onClick={() => removeAdditionalExpense(expense.id)}
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="flex gap-3 pt-4">
              <Button
                onClick={() => onOpenChange(false)}
                variant="outline"
                className="flex-1"
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateInvoice}
                className="flex-1 bg-orange-500 hover:bg-orange-600 text-white"
              >
                Create Final Invoice
              </Button>
            </div>
          </div>

          <EditHistoryPanel
            entityType="invoice"
            entityId="new-invoice"
            comments={comments}
            onAddComment={handleAddComment}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
