import React, { useReducer, ReactNode, useEffect } from "react";
import { Service, CommentEntityType, ServiceStatus } from "@/types";
import { AppState, AppAction, AppContext } from "./AppContextDefinition";

const loadStateFromStorage = (): AppState => {
  try {
    const savedState = localStorage.getItem("crm-state");
    if (savedState) {
      return JSON.parse(savedState);
    }
  } catch (error) {
    console.error("Error loading state from localStorage:", error);
  }
  return {
    customers: [],
    jobsheets: [],
    services: [],
    invoices: [],
    expenses: [],
  };
};

const saveStateToStorage = (state: AppState) => {
  try {
    localStorage.setItem("crm-state", JSON.stringify(state));
  } catch (error) {
    console.error("Error saving state to localStorage:", error);
  }
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  let newState: AppState;

  switch (action.type) {
    case "LOAD_STATE":
      newState = action.payload;
      break;
    case "ADD_CUSTOMER":
      newState = { ...state, customers: [...state.customers, action.payload] };
      break;
    case "UPDATE_CUSTOMER":
      newState = {
        ...state,
        customers: state.customers.map((c) =>
          c.id === action.payload.id ? action.payload : c
        ),
      };
      break;
    case "ADD_JOBSHEET": {
      // Create corresponding service with enhanced logic
      const newService: Service = {
        id: `SRV-${Date.now()}`,
        jobsheetId: action.payload.id,
        description: action.payload.reportedIssues,
        cost: action.payload.estimatedCost,
        status: "pending" as ServiceStatus,
        technicianNotes: "",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        comments: [
          {
            id: `CMT-${Date.now()}`,
            entityType: "service" as CommentEntityType,
            entityId: `SRV-${Date.now()}`,
            content: "Service request created from jobsheet",
            author: "System",
            timestamp: new Date().toISOString(),
          },
        ],
      };
      newState = {
        ...state,
        jobsheets: [...state.jobsheets, action.payload],
        services: [...state.services, newService],
      };
      break;
    }
    case "UPDATE_JOBSHEET": {
      // When updating a jobsheet, sync the related service status
      const updatedJobsheet = action.payload;
      const relatedService = state.services.find(
        (s) => s.jobsheetId === updatedJobsheet.id
      );

      let updatedServices = state.services;
      if (relatedService) {
        // Map jobsheet status to service status
        let serviceStatus: Service["status"] = relatedService.status;
        switch (updatedJobsheet.status) {
          case "pending":
            serviceStatus = "pending";
            break;
          case "in-progress":
            serviceStatus = "in-progress";
            break;
          case "completed":
          case "delivered":
            serviceStatus = "completed";
            break;
        }

        const updatedService = {
          ...relatedService,
          status: serviceStatus,
          updatedAt: new Date().toISOString(),
          comments: [
            ...relatedService.comments,
            {
              id: `CMT-${Date.now()}`,
              entityType: "service" as const,
              entityId: relatedService.id,
              content: `Service status updated to ${serviceStatus} due to jobsheet status change`,
              author: "System",
              timestamp: new Date().toISOString(),
            },
          ],
        };

        updatedServices = state.services.map((s) =>
          s.id === relatedService.id ? updatedService : s
        );
      }

      newState = {
        ...state,
        jobsheets: state.jobsheets.map((j) =>
          j.id === updatedJobsheet.id ? updatedJobsheet : j
        ),
        services: updatedServices,
      };
      break;
    }
    case "ADD_SERVICE":
      newState = { ...state, services: [...state.services, action.payload] };
      break;
    case "UPDATE_SERVICE": {
      // When updating a service, check if we need to sync the jobsheet status
      const updatedService = action.payload;
      const relatedJobsheet = state.jobsheets.find((j) =>
        state.services.some(
          (s) => s.id === updatedService.id && s.jobsheetId === j.id
        )
      );

      let updatedJobsheets = state.jobsheets;
      if (
        relatedJobsheet &&
        updatedService.status === "completed" &&
        relatedJobsheet.status !== "completed"
      ) {
        const updatedJobsheetFromService = {
          ...relatedJobsheet,
          status: "completed" as const,
          updatedAt: new Date().toISOString(),
          comments: [
            ...relatedJobsheet.comments,
            {
              id: `CMT-${Date.now()}`,
              entityType: "jobsheet" as const,
              entityId: relatedJobsheet.id,
              content: "Jobsheet marked as completed due to service completion",
              author: "System",
              timestamp: new Date().toISOString(),
            },
          ],
        };

        updatedJobsheets = state.jobsheets.map((j) =>
          j.id === relatedJobsheet.id ? updatedJobsheetFromService : j
        );
      }

      newState = {
        ...state,
        services: state.services.map((s) =>
          s.id === updatedService.id ? updatedService : s
        ),
        jobsheets: updatedJobsheets,
      };
      break;
    }
    case "ADD_INVOICE":
      newState = { ...state, invoices: [...state.invoices, action.payload] };
      break;
    case "UPDATE_INVOICE":
      newState = {
        ...state,
        invoices: state.invoices.map((i) =>
          i.id === action.payload.id ? action.payload : i
        ),
      };
      break;
    case "ADD_EXPENSE":
      newState = { ...state, expenses: [...state.expenses, action.payload] };
      break;
    case "UPDATE_EXPENSE":
      newState = {
        ...state,
        expenses: state.expenses.map((e) =>
          e.id === action.payload.id ? action.payload : e
        ),
      };
      break;
    default:
      newState = state;
  }

  // Save to localStorage after each state change
  if (action.type !== "LOAD_STATE") {
    saveStateToStorage(newState);
  }

  return newState;
};

export const AppProvider = ({ children }: { children: ReactNode }) => {
  const [state, dispatch] = useReducer(appReducer, loadStateFromStorage());

  useEffect(() => {
    // Load state from localStorage on mount
    const savedState = loadStateFromStorage();
    if (savedState.customers.length > 0 || savedState.jobsheets.length > 0) {
      dispatch({ type: "LOAD_STATE", payload: savedState });
    }
  }, []);

  return (
    <AppContext.Provider value={{ state, dispatch }}>
      {children}
    </AppContext.Provider>
  );
};
