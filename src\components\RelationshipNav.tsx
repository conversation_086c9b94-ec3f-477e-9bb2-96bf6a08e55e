import React from "react";
import { Link } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Users, ClipboardList, FileText } from "lucide-react";
import { useEnhancedAppContext } from "@/context";

interface RelationshipNavProps {
  customerId?: string;
  jobsheetId?: string;
  className?: string;
}

export const RelationshipNav = ({
  customerId,
  jobsheetId,
  className,
}: RelationshipNavProps) => {
  const { state } = useEnhancedAppContext();

  const customer = customerId
    ? state.customers.find((c) => c.id === customerId)
    : null;
  const jobsheet = jobsheetId
    ? state.jobsheets.find((j) => j.id === jobsheetId)
    : null;

  const relatedJobsheets = customerId
    ? state.jobsheets.filter((j) => j.customerId === customerId)
    : [];

  const relatedInvoices = jobsheetId
    ? state.invoices.filter((i) => i.jobsheetId === jobsheetId)
    : customerId
    ? state.invoices.filter((i) => i.customerId === customerId)
    : [];

  if (!customerId && !jobsheetId) return null;

  return (
    <div className={`space-y-2 ${className}`}>
      {customer && (
        <div className="flex items-center gap-2">
          <Users className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-600">Customer:</span>
          <Link
            to="/customers"
            className="text-orange-500 hover:text-orange-600 text-sm font-medium"
          >
            {customer.name}
          </Link>
        </div>
      )}

      {relatedJobsheets.length > 0 && (
        <div className="flex items-center gap-2">
          <ClipboardList className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-600">Jobsheets:</span>
          <div className="flex gap-1">
            {relatedJobsheets.slice(0, 3).map((js) => (
              <Link
                key={js.id}
                to="/jobsheets"
                className="text-orange-500 hover:text-orange-600 text-sm"
              >
                <Badge variant="outline" className="text-xs">
                  {js.id}
                </Badge>
              </Link>
            ))}
            {relatedJobsheets.length > 3 && (
              <Badge variant="outline" className="text-xs text-gray-500">
                +{relatedJobsheets.length - 3} more
              </Badge>
            )}
          </div>
        </div>
      )}

      {relatedInvoices.length > 0 && (
        <div className="flex items-center gap-2">
          <FileText className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-600">Invoices:</span>
          <div className="flex gap-1">
            {relatedInvoices.slice(0, 3).map((invoice) => (
              <Link
                key={invoice.id}
                to="/invoices"
                className="text-orange-500 hover:text-orange-600 text-sm"
              >
                <Badge variant="outline" className="text-xs">
                  {invoice.id}
                </Badge>
              </Link>
            ))}
            {relatedInvoices.length > 3 && (
              <Badge variant="outline" className="text-xs text-gray-500">
                +{relatedInvoices.length - 3} more
              </Badge>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
