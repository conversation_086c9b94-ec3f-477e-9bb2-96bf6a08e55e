import React from 'react';
import { toast } from '@/hooks/use-toast';
import { ToastAction } from '@/components/ui/toast';

export type NotificationType = 'success' | 'error' | 'warning' | 'info';

export interface NotificationAction {
  label: string;
  onClick: () => void;
}

export interface NotificationOptions {
  title?: string;
  description: string;
  duration?: number;
  action?: NotificationAction;
}

export class NotificationManager {
  private static show(type: NotificationType, options: NotificationOptions) {
    const { title, description, duration = 5000, action } = options;
    
    toast({
      title,
      description,
      duration,
      variant: type === 'error' ? 'destructive' : 'default',
      action: action ? (
        <ToastAction altText={action.label} onClick={action.onClick}>
          {action.label}
        </ToastAction>
      ) : undefined
    });
  }

  static success(title: string, description: string, action?: NotificationAction) {
    this.show('success', { title, description, action });
  }

  static error(title: string, description: string, action?: NotificationAction) {
    this.show('error', { title, description, action });
  }

  static warning(title: string, description: string, action?: NotificationAction) {
    this.show('warning', { title, description, action });
  }

  static info(title: string, description: string, action?: NotificationAction) {
    this.show('info', { title, description, action });
  }

  // Business-specific notifications
  static customerCreated(customerName: string) {
    this.success(
      'Customer Created',
      `Customer "${customerName}" has been successfully created`
    );
  }

  static jobsheetCreated(jobsheetId: string) {
    this.success(
      'Jobsheet Created',
      `Jobsheet ${jobsheetId} has been created and a service request has been automatically generated`
    );
  }

  static serviceCompleted(serviceId: string) {
    this.success(
      'Service Completed',
      `Service ${serviceId} has been marked as completed`
    );
  }

  static invoiceGenerated(invoiceId: string) {
    this.success(
      'Invoice Generated',
      `Invoice ${invoiceId} has been successfully generated`
    );
  }

  static statusUpdated(entityType: string, entityId: string, newStatus: string) {
    this.info('Status Updated', `${entityType} ${entityId} status changed to ${newStatus}`);
  }

  static validationErrors(errors: string[]) {
    this.error(
      'Validation Failed',
      `Please fix the following issues: ${errors.join(', ')}`
    );
  }

  static validationWarnings(warnings: string[]) {
    this.warning(
      'Validation Warnings',
      `Please note: ${warnings.join(', ')}`
    );
  }
}
