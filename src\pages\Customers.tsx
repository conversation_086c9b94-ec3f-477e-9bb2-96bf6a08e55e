import React, { useState } from "react";
import { useEnhancedAppContext } from "@/context";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Plus,
  Search,
  Phone,
  Mail,
  MapPin,
  User,
  Edit,
  Eye,
  ClipboardList,
} from "lucide-react";
import { Customer } from "@/types";
import { AddCustomerDialog } from "@/components/AddCustomerDialog";
import { ViewCustomerDialog } from "@/components/ViewCustomerDialog";
import { EditCustomerDialog } from "@/components/EditCustomerDialog";
import { CreateJobsheetDialog } from "@/components/CreateJobsheetDialog";

const Customers = () => {
  const { state } = useEnhancedAppContext();
  const [searchTerm, setSearchTerm] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    null
  );
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isCreateJobsheetOpen, setIsCreateJobsheetOpen] = useState(false);

  const filteredCustomers = state.customers.filter(
    (customer) =>
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone.includes(searchTerm)
  );

  const handleViewCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsViewDialogOpen(true);
  };

  const handleEditCustomer = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsEditDialogOpen(true);
  };

  const handleCreateJobsheet = (customer: Customer) => {
    setSelectedCustomer(customer);
    setIsCreateJobsheetOpen(true);
  };

  const getCustomerStats = (customerId: string) => {
    const jobsheets = state.jobsheets.filter(
      (j) => j.customerId === customerId
    );
    const invoices = state.invoices.filter((i) => i.customerId === customerId);
    const totalSpent = invoices.reduce(
      (sum, invoice) => sum + invoice.total,
      0
    );

    return {
      totalJobsheets: jobsheets.length,
      totalSpent,
      activeJobsheets: jobsheets.filter((j) => j.status !== "delivered").length,
    };
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Customers</h1>
          <p className="text-gray-600">Manage your customer database</p>
        </div>
        <Button
          onClick={() => setIsCreateDialogOpen(true)}
          className="bg-orange-500 hover:bg-orange-600"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Customer
        </Button>
      </div>

      <div className="flex gap-4 mb-6">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search customers..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>

      <div className="grid gap-4">
        {filteredCustomers.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No customers found</p>
            </CardContent>
          </Card>
        ) : (
          filteredCustomers.map((customer) => {
            const stats = getCustomerStats(customer.id);
            return (
              <Card key={customer.id}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-3">
                        <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center">
                          <User className="h-6 w-6 text-orange-600" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">
                            {customer.name}
                          </h3>
                          <p className="text-gray-500">
                            Customer ID: {customer.id}
                          </p>
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div className="flex items-center gap-2 text-gray-600">
                          <Mail className="h-4 w-4" />
                          {customer.email}
                        </div>
                        <div className="flex items-center gap-2 text-gray-600">
                          <Phone className="h-4 w-4" />
                          {customer.phone}
                        </div>
                        <div className="flex items-center gap-2 text-gray-600">
                          <MapPin className="h-4 w-4" />
                          {customer.address}
                        </div>
                      </div>
                    </div>
                    <div className="text-right mr-4">
                      <div className="grid grid-cols-3 gap-4 text-center">
                        <div>
                          <p className="text-lg font-semibold text-orange-600">
                            {stats.totalJobsheets}
                          </p>
                          <p className="text-xs text-gray-500">Total Jobs</p>
                        </div>
                        <div>
                          <p className="text-lg font-semibold text-green-600">
                            ${stats.totalSpent.toFixed(2)}
                          </p>
                          <p className="text-xs text-gray-500">Total Spent</p>
                        </div>
                        <div>
                          <p className="text-lg font-semibold text-blue-600">
                            {stats.activeJobsheets}
                          </p>
                          <p className="text-xs text-gray-500">Active</p>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewCustomer(customer)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleCreateJobsheet(customer)}
                        className="bg-orange-50 hover:bg-orange-100"
                      >
                        <ClipboardList className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleEditCustomer(customer)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })
        )}
      </div>

      <AddCustomerDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
      />

      <ViewCustomerDialog
        customer={selectedCustomer}
        open={isViewDialogOpen}
        onOpenChange={setIsViewDialogOpen}
      />

      <EditCustomerDialog
        customer={selectedCustomer}
        open={isEditDialogOpen}
        onOpenChange={setIsEditDialogOpen}
      />

      <CreateJobsheetDialog
        open={isCreateJobsheetOpen}
        onOpenChange={setIsCreateJobsheetOpen}
        preselectedCustomer={selectedCustomer}
      />
    </div>
  );
};

export default Customers;
