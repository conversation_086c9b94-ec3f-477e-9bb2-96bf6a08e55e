import React from "react";
import { useEnhancedAppContext } from "@/context";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Service } from "@/types";
import { EditHistoryPanel } from "./EditHistoryPanel";

interface ViewServiceDialogProps {
  service: Service;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ViewServiceDialog = ({
  service,
  open,
  onOpenChange,
}: ViewServiceDialogProps) => {
  const { state } = useEnhancedAppContext();
  const jobsheet = state.jobsheets.find((j) => j.id === service.jobsheetId);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending":
        return "bg-orange-100 text-orange-800";
      case "in-progress":
        return "bg-blue-100 text-blue-800";
      case "completed":
        return "bg-green-100 text-green-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>View Service - {service.id}</DialogTitle>
        </DialogHeader>

        <div className="flex h-[calc(70vh)]">
          <div className="flex-1 overflow-y-auto pr-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Service ID</Label>
                  <Input value={service.id} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Jobsheet ID</Label>
                  <Input
                    value={service.jobsheetId}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
              </div>

              <div>
                <Label>Customer</Label>
                <Input
                  value={jobsheet?.customerName || "Unknown"}
                  readOnly
                  className="bg-gray-50"
                />
              </div>

              <div>
                <Label>Description</Label>
                <Textarea
                  value={service.description}
                  readOnly
                  className="bg-gray-50"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Cost</Label>
                  <Input
                    value={`$${service.cost.toFixed(2)}`}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label>Status</Label>
                  <div className="mt-2">
                    <Badge className={getStatusColor(service.status)}>
                      {service.status}
                    </Badge>
                  </div>
                </div>
              </div>

              <div>
                <Label>Technician Notes</Label>
                <Textarea
                  value={service.technicianNotes || "No notes available"}
                  readOnly
                  className="bg-gray-50"
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Created At</Label>
                  <Input
                    value={new Date(service.createdAt).toLocaleString()}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label>Updated At</Label>
                  <Input
                    value={new Date(service.updatedAt).toLocaleString()}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
              </div>

              <Button onClick={() => onOpenChange(false)} className="w-full">
                Close
              </Button>
            </div>
          </div>

          <EditHistoryPanel
            entityType="service"
            entityId={service.id}
            comments={service.comments || []}
            onAddComment={() => {}} // Read-only mode
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
