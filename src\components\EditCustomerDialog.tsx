import React, { useState } from "react";
import { useEnhancedAppContext } from "@/context";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Customer, Comment } from "@/types";
import { EditHistoryPanel } from "./EditHistoryPanel";

interface EditCustomerDialogProps {
  customer: Customer | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const EditCustomerDialog = ({
  customer,
  open,
  onOpenChange,
}: EditCustomerDialogProps) => {
  const { dispatch } = useEnhancedAppContext();
  const [comments, setComments] = useState<Comment[]>([]);

  // Don't render if customer is null
  if (!customer) {
    return null;
  }

  const handleAddComment = (comment: Omit<Comment, "id" | "timestamp">) => {
    const newComment: Comment = {
      ...comment,
      id: `CMT-${Date.now()}-${Math.random()}`,
      timestamp: new Date().toISOString(),
    };
    setComments((prev) => [...prev, newComment]);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    const updateComment: Comment = {
      id: `CMT-${Date.now()}-update`,
      entityType: "customer",
      entityId: customer.id,
      content: "Customer details updated",
      author: "System",
      timestamp: new Date().toISOString(),
    };

    const updatedCustomer: Customer = {
      ...customer,
      name: formData.get("name") as string,
      phone: formData.get("phone") as string,
      alternatePhone: (formData.get("alternatePhone") as string) || undefined,
      email: formData.get("email") as string,
      address: formData.get("address") as string,
      updatedAt: new Date().toISOString(),
      comments: [updateComment, ...comments, ...customer.comments],
    };

    dispatch({ type: "UPDATE_CUSTOMER", payload: updatedCustomer });
    onOpenChange(false);
    setComments([]);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Edit Customer - {customer.name}</DialogTitle>
        </DialogHeader>

        <div className="flex h-[calc(70vh)]">
          <div className="flex-1 overflow-y-auto pr-4">
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <Label>Customer ID</Label>
                <Input value={customer.id} readOnly className="bg-gray-50" />
              </div>

              <div>
                <Label htmlFor="name">Full Name *</Label>
                <Input
                  id="name"
                  name="name"
                  defaultValue={customer.name}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone">Phone Number *</Label>
                  <Input
                    id="phone"
                    name="phone"
                    defaultValue={customer.phone}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="alternatePhone">Alternate Phone</Label>
                  <Input
                    id="alternatePhone"
                    name="alternatePhone"
                    defaultValue={customer.alternatePhone || ""}
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="email">Email Address *</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  defaultValue={customer.email}
                  required
                />
              </div>

              <div>
                <Label htmlFor="address">Address *</Label>
                <Textarea
                  id="address"
                  name="address"
                  defaultValue={customer.address}
                  required
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Created At</Label>
                  <Input
                    value={new Date(customer.createdAt).toLocaleString()}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
                <div>
                  <Label>Updated At</Label>
                  <Input
                    value={new Date(customer.updatedAt).toLocaleString()}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <Button type="submit" className="flex-1">
                  Update Customer
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => onOpenChange(false)}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </div>

          <EditHistoryPanel
            entityType="customer"
            entityId={customer.id}
            comments={comments}
            onAddComment={handleAddComment}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
