import React, { useState } from "react";
import { useEnhancedAppContext } from "@/context";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Search,
  Plus,
  ClipboardList,
  Smartphone,
  Laptop,
  Monitor,
} from "lucide-react";
import { Customer, Jobsheet, Comment } from "@/types";
import { generateDeliveryDate } from "@/utils/dateUtils";
import { AddCustomerDialog } from "./AddCustomerDialog";
import { EditHistoryPanel } from "./EditHistoryPanel";

interface CreateJobsheetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  preselectedCustomer?: Customer;
}

const deviceTypes = [
  { value: "laptop", label: "Laptop", icon: Laptop },
  { value: "smartphone", label: "Smartphone", icon: Smartphone },
  { value: "tablet", label: "Tablet", icon: Monitor },
  { value: "desktop", label: "Desktop", icon: Monitor },
  { value: "other", label: "Other", icon: Monitor },
];

const statuses = [
  { value: "pending", label: "Pending" },
  { value: "in-progress", label: "In Progress" },
  { value: "approval", label: "Approval" },
  { value: "ready-return", label: "Ready&Return" },
  { value: "completed", label: "Completed" },
];

export const CreateJobsheetDialog = ({
  open,
  onOpenChange,
  preselectedCustomer,
}: CreateJobsheetDialogProps) => {
  const { state, dispatch } = useEnhancedAppContext();

  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(
    preselectedCustomer || null
  );
  const [showNewCustomerDialog, setShowNewCustomerDialog] = useState(false);
  const [comments, setComments] = useState<Comment[]>([]);

  const filteredCustomers = state.customers.filter(
    (customer) =>
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone.includes(searchTerm) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCustomerSelect = (customer: Customer) => {
    setSelectedCustomer(customer);
    setSearchTerm("");
  };

  const handleNewCustomerCreated = (customer: Customer) => {
    setSelectedCustomer(customer);
    setShowNewCustomerDialog(false);
  };

  const handleAddComment = (comment: Omit<Comment, "id" | "timestamp">) => {
    const newComment: Comment = {
      ...comment,
      id: `CMT-${Date.now()}-${Math.random()}`,
      timestamp: new Date().toISOString(),
    };
    setComments((prev) => [...prev, newComment]);
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!selectedCustomer) return;

    const formData = new FormData(e.currentTarget);

    const jobsheetId = `JS-${Date.now()}`;
    const creationComment: Comment = {
      id: `CMT-${Date.now()}-creation`,
      entityType: "jobsheet",
      entityId: jobsheetId,
      content: "Jobsheet created",
      author: "System",
      timestamp: new Date().toISOString(),
    };

    const newJobsheet: Jobsheet = {
      id: jobsheetId,
      customerId: selectedCustomer.id,
      customerName: selectedCustomer.name,
      deviceType: formData.get("deviceType") as string,
      brand: formData.get("brand") as string,
      model: formData.get("model") as string,
      serialNumber: (formData.get("serialNumber") as string) || undefined,
      devicePassword: (formData.get("devicePassword") as string) || undefined,
      reportedIssues: formData.get("reportedIssues") as string,
      physicalCondition: formData.get("physicalCondition") as string,
      accessoriesReceived: formData.get("accessoriesReceived") as string,
      estimatedCost: Number(formData.get("estimatedCost")) || 0,
      advancePayment: Number(formData.get("advancePayment")) || 0,
      expectedDelivery:
        (formData.get("expectedDelivery") as string) || generateDeliveryDate(),
      technicianAssigned:
        (formData.get("technicianAssigned") as string) || undefined,
      priority: "medium",
      warrantyTerms: (formData.get("warrantyTerms") as string) || undefined,
      specialInstructions:
        (formData.get("specialInstructions") as string) || undefined,
      receivedBy: formData.get("receivedBy") as string,
      customerSignatureObtained:
        formData.get("customerSignatureObtained") === "on",
      termsAccepted: formData.get("termsAccepted") === "on",
      status:
        (formData.get("status") as
          | "pending"
          | "in-progress"
          | "completed"
          | "delivered") || "pending",
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      comments: [creationComment, ...comments],
    };

    dispatch({ type: "ADD_JOBSHEET", payload: newJobsheet });
    onOpenChange(false);
    setSelectedCustomer(preselectedCustomer || null);
    setSearchTerm("");
    setComments([]);
  };

  React.useEffect(() => {
    if (!open) {
      setSelectedCustomer(preselectedCustomer || null);
      setComments([]);
    }
  }, [open, preselectedCustomer]);

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <ClipboardList className="h-5 w-5" />
              Create New Jobsheet
            </DialogTitle>
          </DialogHeader>

          <div className="flex h-[calc(90vh-120px)]">
            <div className="flex-1 overflow-y-auto pr-4">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Customer Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">
                    Customer Information
                  </h3>

                  {!selectedCustomer ? (
                    <div className="space-y-4">
                      <div className="flex gap-2">
                        <div className="flex-1 relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                          <Input
                            placeholder="Search customer by name, phone, or email..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10"
                          />
                        </div>
                        <Button
                          type="button"
                          onClick={() => setShowNewCustomerDialog(true)}
                          className="bg-orange-500 hover:bg-orange-600"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          New Customer
                        </Button>
                      </div>

                      {searchTerm && (
                        <div className="border rounded-md max-h-40 overflow-y-auto">
                          {filteredCustomers.map((customer) => (
                            <div
                              key={customer.id}
                              className="p-3 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
                              onClick={() => handleCustomerSelect(customer)}
                            >
                              <div className="font-medium">{customer.name}</div>
                              <div className="text-sm text-gray-500">
                                {customer.phone} • {customer.email}
                              </div>
                            </div>
                          ))}
                          {filteredCustomers.length === 0 && (
                            <div className="p-3 text-gray-500">
                              No customers found
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                      <div>
                        <Label>Customer Name *</Label>
                        <Input
                          value={selectedCustomer.name}
                          readOnly
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label>Phone Number *</Label>
                        <Input
                          value={selectedCustomer.phone}
                          readOnly
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label>Alternate Phone</Label>
                        <Input
                          value={selectedCustomer.alternatePhone || ""}
                          readOnly
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label>Email</Label>
                        <Input
                          value={selectedCustomer.email}
                          readOnly
                          className="mt-1"
                        />
                      </div>
                      <div className="md:col-span-2">
                        <Label>Address</Label>
                        <Textarea
                          value={selectedCustomer.address}
                          readOnly
                          className="mt-1"
                        />
                      </div>
                      {!preselectedCustomer && (
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => setSelectedCustomer(null)}
                          className="md:col-span-2"
                        >
                          Change Customer
                        </Button>
                      )}
                    </div>
                  )}
                </div>

                {selectedCustomer && (
                  <>
                    {/* Device Information */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">
                        Device Information
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label htmlFor="deviceType">Device Type *</Label>
                          <Select name="deviceType" required>
                            <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Select device type" />
                            </SelectTrigger>
                            <SelectContent>
                              {deviceTypes.map((type) => (
                                <SelectItem key={type.value} value={type.value}>
                                  <div className="flex items-center gap-2">
                                    <type.icon className="h-4 w-4" />
                                    {type.label}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="brand">Brand *</Label>
                          <Input
                            id="brand"
                            name="brand"
                            placeholder="e.g., Dell, HP, Lenovo"
                            required
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="model">Model *</Label>
                          <Input
                            id="model"
                            name="model"
                            placeholder="e.g., Inspiron 15 3000"
                            required
                            className="mt-1"
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="serialNumber">Serial Number</Label>
                          <Input
                            id="serialNumber"
                            name="serialNumber"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="devicePassword">
                            Device Password
                          </Label>
                          <Input
                            id="devicePassword"
                            name="devicePassword"
                            placeholder="For system access if needed"
                            className="mt-1"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Service Details */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">Service Details</h3>
                      <div>
                        <Label htmlFor="reportedIssues">
                          Reported Issues *
                        </Label>
                        <Textarea
                          id="reportedIssues"
                          name="reportedIssues"
                          placeholder="Describe the issues reported by the customer..."
                          required
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="physicalCondition">
                          Physical Condition *
                        </Label>
                        <Textarea
                          id="physicalCondition"
                          name="physicalCondition"
                          placeholder="Note any visible damage, scratches, or physical condition..."
                          required
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="accessoriesReceived">
                          Accessories Received *
                        </Label>
                        <Textarea
                          id="accessoriesReceived"
                          name="accessoriesReceived"
                          placeholder="List all accessories received (charger, bag, mouse, etc.)"
                          required
                          className="mt-1"
                        />
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                          <Label htmlFor="estimatedCost">Estimated Cost</Label>
                          <Input
                            id="estimatedCost"
                            name="estimatedCost"
                            type="number"
                            defaultValue="0"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="advancePayment">
                            Advance Payment
                          </Label>
                          <Input
                            id="advancePayment"
                            name="advancePayment"
                            type="number"
                            defaultValue="0"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="expectedDelivery">
                            Expected Delivery
                          </Label>
                          <Input
                            id="expectedDelivery"
                            name="expectedDelivery"
                            type="date"
                            defaultValue={generateDeliveryDate()}
                            className="mt-1"
                          />
                        </div>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="technicianAssigned">
                            Technician Assigned
                          </Label>
                          <Input
                            id="technicianAssigned"
                            name="technicianAssigned"
                            className="mt-1"
                          />
                        </div>
                        <div>
                          <Label htmlFor="status">Status</Label>
                          <Select name="status" defaultValue="pending">
                            <SelectTrigger className="mt-1">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {statuses.map((status) => (
                                <SelectItem
                                  key={status.value}
                                  value={status.value}
                                >
                                  {status.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </div>

                    {/* Additional Information */}
                    <div className="space-y-4">
                      <h3 className="text-lg font-semibold">
                        Additional Information
                      </h3>
                      <div>
                        <Label htmlFor="warrantyTerms">Warranty Terms</Label>
                        <Textarea
                          id="warrantyTerms"
                          name="warrantyTerms"
                          placeholder="Warranty terms and conditions..."
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="specialInstructions">
                          Special Instructions
                        </Label>
                        <Textarea
                          id="specialInstructions"
                          name="specialInstructions"
                          placeholder="Any special instructions or notes..."
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label htmlFor="receivedBy">Received By *</Label>
                        <Input
                          id="receivedBy"
                          name="receivedBy"
                          placeholder="Staff member name"
                          required
                          className="mt-1"
                        />
                      </div>

                      <div className="space-y-3">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="customerSignatureObtained"
                            name="customerSignatureObtained"
                          />
                          <Label htmlFor="customerSignatureObtained">
                            Customer Signature Obtained
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox id="termsAccepted" name="termsAccepted" />
                          <Label htmlFor="termsAccepted">
                            Terms & Conditions Accepted
                          </Label>
                        </div>
                      </div>
                    </div>

                    <div className="flex justify-end gap-2">
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => onOpenChange(false)}
                      >
                        Cancel
                      </Button>
                      <Button
                        type="submit"
                        className="bg-orange-500 hover:bg-orange-600"
                      >
                        Create Jobsheet
                      </Button>
                    </div>
                  </>
                )}
              </form>
            </div>

            <EditHistoryPanel
              entityType="jobsheet"
              entityId="new-jobsheet"
              comments={comments}
              onAddComment={handleAddComment}
            />
          </div>
        </DialogContent>
      </Dialog>

      <AddCustomerDialog
        open={showNewCustomerDialog}
        onOpenChange={setShowNewCustomerDialog}
        onCustomerCreated={handleNewCustomerCreated}
      />
    </>
  );
};
