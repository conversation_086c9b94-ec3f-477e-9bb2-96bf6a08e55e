import { useReducer, ReactNode, useEffect } from "react";
import { Service } from "@/types";
import { BusinessValidator } from "@/utils/businessValidation";
import { NotificationManager } from "@/utils/notifications";
import { DataSynchronizer } from "@/utils/dataSynchronizer";
import { AppState, AppAction } from "./types";
import { EnhancedAppContext } from "./EnhancedAppContextDefinition";
import { customer } from "@/api";

const loadStateFromStorage = async (): Promise<
  Omit<AppState, "syncInProgress" | "lastSyncTime">
> => {
  try {
    const customerData = await customer.getAll();
    return {
      customers: customerData,
      jobsheets: [],
      services: [],
      invoices: [],
      expenses: [],
    };
  } catch (error) {
    console.error("Error loading state from localStorage:", error);
  }
  return {
    customers: [],
    jobsheets: [],
    services: [],
    invoices: [],
    expenses: [],
  };
};

const saveStateToStorage = (state: AppState) => {
  try {
    const { syncInProgress, lastSyncTime, ...stateToSave } = state;
    localStorage.setItem("crm-state", JSON.stringify(stateToSave));
  } catch (error) {
    console.error("Error saving state to localStorage:", error);
  }
};

const enhancedAppReducer = (state: AppState, action: AppAction): AppState => {
  let newState: AppState;

  switch (action.type) {
    case "SET_SYNC_STATUS":
      return { ...state, syncInProgress: action.payload };

    case "LOAD_STATE":
      return {
        ...action.payload,
        syncInProgress: false,
        lastSyncTime: new Date().toISOString(),
      };

    case "ADD_CUSTOMER": {
      // Validate customer before adding
      const customerValidation = BusinessValidator.validateCustomer(
        action.payload
      );
      if (!customerValidation.isValid) {
        NotificationManager.validationErrors(customerValidation.errors);
        return state;
      }

      if (customerValidation.warnings.length > 0) {
        NotificationManager.validationWarnings(customerValidation.warnings);        
        return state;
      }

      
      try {
        console.log("responseData");
        const responseData = Promise.resolve(
          customer.add({
            name: action.payload.name,
            email: action.payload.email,
            phone: action.payload.phone,
            alternatePhone: action.payload.alternatePhone,
            address: action.payload.address,
            state: action.payload.state,
            city: action.payload.city,
            pincode: action.payload.pincode,
            comments: action.payload.comments,
          })
        ).then((response) => {
          return response.data;
        });

        console.log("responseData", responseData);

      } catch {
        NotificationManager.error(
          "Customer Creation Failed",
          "Failed to create customer. Please try again."
        );
      }

      newState = { ...state, customers: [...state.customers, action.payload] };
      NotificationManager.customerCreated(action.payload.name);
      break;
    }

    case "UPDATE_CUSTOMER":
      newState = {
        ...state,
        customers: state.customers.map((c) =>
          c.id === action.payload.id ? action.payload : c
        ),
      };
      break;

    case "ADD_JOBSHEET": {
      // Validate jobsheet
      const jobsheetValidation = BusinessValidator.validateJobsheet(
        action.payload
      );
      if (!jobsheetValidation.isValid) {
        NotificationManager.validationErrors(jobsheetValidation.errors);
        return state;
      }

      if (jobsheetValidation.warnings.length > 0) {
        NotificationManager.validationWarnings(jobsheetValidation.warnings);
      }

      // Create corresponding service with enhanced logic
      const newService: Service = {
        id: `SRV-${Date.now()}`,
        jobsheetId: action.payload.id,
        description: action.payload.reportedIssues,
        cost: action.payload.estimatedCost,
        status: "pending",
        technicianNotes: "",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        comments: [
          {
            id: `CMT-${Date.now()}`,
            entityType: "service",
            entityId: `SRV-${Date.now()}`,
            content: `Service request auto-generated from jobsheet ${action.payload.id}`,
            author: "System",
            timestamp: new Date().toISOString(),
          },
        ],
      };

      newState = {
        ...state,
        jobsheets: [...state.jobsheets, action.payload],
        services: [...state.services, newService],
      };

      NotificationManager.jobsheetCreated(action.payload.id);
      break;
    }

    case "UPDATE_JOBSHEET": {
      const updatedJobsheet = action.payload;
      const relatedService = state.services.find(
        (s) => s.jobsheetId === updatedJobsheet.id
      );

      let updatedServices = state.services;
      if (relatedService) {
        const syncResult = DataSynchronizer.syncJobsheetToService(
          updatedJobsheet,
          relatedService
        );
        if (syncResult.changes.length > 0) {
          updatedServices = state.services.map((s) =>
            s.id === relatedService.id ? syncResult.service : s
          );
          NotificationManager.info(
            "Auto-Sync",
            `Service ${relatedService.id} synced with jobsheet changes`
          );
        }
      }

      newState = {
        ...state,
        jobsheets: state.jobsheets.map((j) =>
          j.id === updatedJobsheet.id ? updatedJobsheet : j
        ),
        services: updatedServices,
      };
      break;
    }

    case "UPDATE_SERVICE": {
      const updatedService = action.payload;

      // Validate service
      const serviceValidation =
        BusinessValidator.validateService(updatedService);
      if (serviceValidation.warnings.length > 0) {
        NotificationManager.validationWarnings(serviceValidation.warnings);
      }

      const relatedJobsheet = state.jobsheets.find((j) =>
        state.services.some(
          (s) => s.id === updatedService.id && s.jobsheetId === j.id
        )
      );

      let updatedJobsheets = state.jobsheets;
      if (relatedJobsheet && updatedService.status === "completed") {
        const syncResult = DataSynchronizer.syncServiceToJobsheet(
          updatedService,
          relatedJobsheet
        );
        if (syncResult.changes.length > 0) {
          updatedJobsheets = state.jobsheets.map((j) =>
            j.id === relatedJobsheet.id ? syncResult.jobsheet : j
          );
          NotificationManager.info(
            "Auto-Sync",
            `Jobsheet ${relatedJobsheet.id} synced with service completion`
          );
        }
      }

      newState = {
        ...state,
        services: state.services.map((s) =>
          s.id === updatedService.id ? updatedService : s
        ),
        jobsheets: updatedJobsheets,
      };

      if (updatedService.status === "completed") {
        NotificationManager.serviceCompleted(updatedService.id);
      }
      break;
    }

    case "ADD_INVOICE": {
      // Validate invoice
      const invoiceValidation = BusinessValidator.validateInvoice(
        action.payload
      );
      if (!invoiceValidation.isValid) {
        NotificationManager.validationErrors(invoiceValidation.errors);
        return state;
      }

      if (invoiceValidation.warnings.length > 0) {
        NotificationManager.validationWarnings(invoiceValidation.warnings);
      }

      // Check invoice integrity
      const relatedServices = state.services.filter((s) =>
        action.payload.serviceIds.includes(s.id)
      );
      const invoiceIssues = DataSynchronizer.validateInvoiceIntegrity(
        action.payload,
        relatedServices
      );

      if (invoiceIssues.length > 0) {
        NotificationManager.warning("Invoice Issues", invoiceIssues.join(", "));
      }

      newState = { ...state, invoices: [...state.invoices, action.payload] };
      NotificationManager.invoiceGenerated(action.payload.id);
      break;
    }

    case "UPDATE_INVOICE":
      newState = {
        ...state,
        invoices: state.invoices.map((i) =>
          i.id === action.payload.id ? action.payload : i
        ),
      };
      break;

    case "ADD_EXPENSE": {
      // Validate expense
      const expenseValidation = BusinessValidator.validateExpense(
        action.payload
      );
      if (!expenseValidation.isValid) {
        NotificationManager.validationErrors(expenseValidation.errors);
        return state;
      }

      if (expenseValidation.warnings.length > 0) {
        NotificationManager.validationWarnings(expenseValidation.warnings);
      }

      newState = { ...state, expenses: [...state.expenses, action.payload] };
      break;
    }

    case "UPDATE_EXPENSE":
      newState = {
        ...state,
        expenses: state.expenses.map((e) =>
          e.id === action.payload.id ? action.payload : e
        ),
      };
      break;

    case "BULK_UPDATE_SERVICES":
      newState = {
        ...state,
        services: action.payload,
      };
      break;

    case "BULK_UPDATE_JOBSHEETS":
      newState = {
        ...state,
        jobsheets: action.payload,
      };
      break;

    default:
      newState = state;
  }

  // Save to localStorage and update sync time for data-changing actions
  newState = { ...newState, lastSyncTime: new Date().toISOString() };
  saveStateToStorage(newState);

  return newState;
};

export const EnhancedAppProvider = ({ children }: { children: ReactNode }) => {
  const initialState: AppState = {
    customers: [],
    jobsheets: [],
    services: [],
    invoices: [],
    expenses: [],
    syncInProgress: false,
    lastSyncTime: null,
  };

  const [state, dispatch] = useReducer(enhancedAppReducer, initialState);

  useEffect(() => {
    // Load state from localStorage on mount
    const loadData = async () => {
      const savedState = await loadStateFromStorage();
      if (savedState.customers.length > 0 || savedState.jobsheets.length > 0) {
        dispatch({ type: "LOAD_STATE", payload: savedState });
      }
    };
    loadData();
  }, []);

  const performDataSync = async () => {
    dispatch({ type: "SET_SYNC_STATUS", payload: true });

    try {
      // Generate sync report and fix issues
      const syncReport = DataSynchronizer.generateSyncReport(
        state.jobsheets,
        state.services,
        state.invoices
      );

      // Fix misaligned statuses
      const updatedServices: Service[] = [];

      syncReport.misalignedStatuses.forEach(({ jobsheet, service }) => {
        const syncResult = DataSynchronizer.syncJobsheetToService(
          jobsheet,
          service
        );
        if (syncResult.changes.length > 0) {
          updatedServices.push(syncResult.service);
        }
      });

      if (updatedServices.length > 0) {
        const newServices = state.services.map((s) => {
          const updated = updatedServices.find((us) => us.id === s.id);
          return updated || s;
        });
        dispatch({ type: "BULK_UPDATE_SERVICES", payload: newServices });
      }

      NotificationManager.success(
        "Data Sync Complete",
        `Synchronized ${updatedServices.length} services and resolved data inconsistencies`
      );
    } catch (error) {
      NotificationManager.error("Sync Error", "Failed to synchronize data");
    } finally {
      dispatch({ type: "SET_SYNC_STATUS", payload: false });
    }
  };

  const generateSyncReport = () => {
    return DataSynchronizer.generateSyncReport(
      state.jobsheets,
      state.services,
      state.invoices
    );
  };

  return (
    <EnhancedAppContext.Provider
      value={{
        state,
        dispatch,
        performDataSync,
        generateSyncReport,
      }}
    >
      {children}
    </EnhancedAppContext.Provider>
  );
};
