import React, { useState } from "react";
import { useEnhancedAppContext } from "@/context";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Service, Invoice, Comment } from "@/types";
import { EditHistoryPanel } from "./EditHistoryPanel";

interface CreateInvoiceFromServiceDialogProps {
  service: Service;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const CreateInvoiceFromServiceDialog = ({
  service,
  open,
  onOpenChange,
}: CreateInvoiceFromServiceDialogProps) => {
  const { state, dispatch } = useEnhancedAppContext();
  const [comments, setComments] = useState<Comment[]>([]);

  const jobsheet = state.jobsheets.find((j) => j.id === service.jobsheetId);
  const customer = state.customers.find((c) => c.id === jobsheet?.customerId);
  const subtotal = service.cost;
  const tax = subtotal * 0.1; // 10% tax
  const total = subtotal + tax;

  const handleAddComment = (comment: Omit<Comment, "id" | "timestamp">) => {
    const newComment: Comment = {
      ...comment,
      id: `CMT-${Date.now()}-${Math.random()}`,
      timestamp: new Date().toISOString(),
    };
    setComments((prev) => [...prev, newComment]);
  };

  const handleCreateInvoice = () => {
    if (!jobsheet || !customer) return;

    const invoiceId = `INV-${Date.now()}`;
    const creationComment: Comment = {
      id: `CMT-${Date.now()}-creation`,
      entityType: "invoice",
      entityId: invoiceId,
      content: `Invoice created from service ${service.id} for jobsheet ${jobsheet.id}`,
      author: "System",
      timestamp: new Date().toISOString(),
    };

    const newInvoice: Invoice = {
      id: invoiceId,
      customerId: customer.id,
      customerName: customer.name,
      customerPhone: customer.phone,
      customerAddress: customer.address,
      jobsheetId: jobsheet.id,
      serviceIds: [service.id],
      deviceInfo: `${jobsheet.deviceType} ${jobsheet.brand} ${jobsheet.model}`,
      subtotal,
      tax,
      total,
      status: "draft",
      issueDate: new Date().toISOString().split("T")[0],
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
        .toISOString()
        .split("T")[0],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      comments: [creationComment, ...comments],
    };

    dispatch({ type: "ADD_INVOICE", payload: newInvoice });
    onOpenChange(false);
    setComments([]);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>Create Invoice from Service</DialogTitle>
        </DialogHeader>

        <div className="flex h-[calc(70vh)]">
          <div className="flex-1 overflow-y-auto pr-4">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Service ID</Label>
                  <Input value={service.id} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Jobsheet ID</Label>
                  <Input
                    value={service.jobsheetId}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
              </div>

              <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
                <h3 className="font-semibold">Customer Details</h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Customer Name</Label>
                    <Input
                      value={customer?.name || "Unknown"}
                      readOnly
                      className="bg-white"
                    />
                  </div>
                  <div>
                    <Label>Phone</Label>
                    <Input
                      value={customer?.phone || "N/A"}
                      readOnly
                      className="bg-white"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Email</Label>
                    <Input
                      value={customer?.email || "N/A"}
                      readOnly
                      className="bg-white"
                    />
                  </div>
                  <div>
                    <Label>Device</Label>
                    <Input
                      value={
                        jobsheet
                          ? `${jobsheet.deviceType} ${jobsheet.brand} ${jobsheet.model}`
                          : "N/A"
                      }
                      readOnly
                      className="bg-white"
                    />
                  </div>
                </div>
                <div>
                  <Label>Address</Label>
                  <Input
                    value={customer?.address || "N/A"}
                    readOnly
                    className="bg-white"
                  />
                </div>
              </div>

              <div>
                <Label>Service Description</Label>
                <Input
                  value={service.description}
                  readOnly
                  className="bg-gray-50"
                />
              </div>

              <div className="border rounded-lg p-4 bg-gray-50">
                <h3 className="font-semibold mb-3">Invoice Summary</h3>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Service Cost:</span>
                    <span>${subtotal.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax (10%):</span>
                    <span>${tax.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between font-semibold text-lg border-t pt-2">
                    <span>Total:</span>
                    <span>${total.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              <div className="flex gap-2">
                <Button onClick={handleCreateInvoice} className="flex-1">
                  Create Invoice
                </Button>
                <Button variant="outline" onClick={() => onOpenChange(false)}>
                  Cancel
                </Button>
              </div>
            </div>
          </div>

          <EditHistoryPanel
            entityType="invoice"
            entityId="new-invoice"
            comments={comments}
            onAddComment={handleAddComment}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
