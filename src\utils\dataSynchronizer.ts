
import { Jobsheet, Service, Invoice, Comment } from '@/types';

export interface SyncResult {
  updated: boolean;
  changes: string[];
}

export class DataSynchronizer {
  static syncJobsheetToService(jobsheet: Jobsheet, service: Service): { service: Service; changes: string[] } {
    const changes: string[] = [];
    let updatedService = { ...service };

    // Sync status
    const statusMapping: Record<Jobsheet['status'], Service['status']> = {
      'pending': 'pending',
      'in-progress': 'in-progress',
      'completed': 'completed',
      'delivered': 'completed'
    };

    const newServiceStatus = statusMapping[jobsheet.status];
    if (service.status !== newServiceStatus) {
      updatedService.status = newServiceStatus;
      changes.push(`Status updated from ${service.status} to ${newServiceStatus}`);
    }

    // Sync description if it's generic
    if (service.description === jobsheet.reportedIssues && 
        service.description !== jobsheet.reportedIssues) {
      updatedService.description = jobsheet.reportedIssues;
      changes.push('Description synced with jobsheet reported issues');
    }

    // Sync cost if service cost matches estimated cost
    if (service.cost === 0 || service.cost === jobsheet.estimatedCost) {
      if (service.cost !== jobsheet.estimatedCost) {
        updatedService.cost = jobsheet.estimatedCost;
        changes.push(`Cost updated to ${jobsheet.estimatedCost}`);
      }
    }

    // Add sync comment if changes were made
    if (changes.length > 0) {
      const syncComment: Comment = {
        id: `CMT-${Date.now()}-sync`,
        entityType: 'service',
        entityId: service.id,
        content: `Auto-synced from jobsheet: ${changes.join(', ')}`,
        author: 'System',
        timestamp: new Date().toISOString()
      };

      updatedService = {
        ...updatedService,
        comments: [syncComment, ...updatedService.comments],
        updatedAt: new Date().toISOString()
      };
    }

    return { service: updatedService, changes };
  }

  static syncServiceToJobsheet(service: Service, jobsheet: Jobsheet): { jobsheet: Jobsheet; changes: string[] } {
    const changes: string[] = [];
    let updatedJobsheet = { ...jobsheet };

    // Only sync if service is completed and jobsheet isn't delivered yet
    if (service.status === 'completed' && 
        jobsheet.status !== 'completed' && 
        jobsheet.status !== 'delivered') {
      
      updatedJobsheet.status = 'completed';
      changes.push('Status updated to completed due to service completion');

      // Add sync comment
      const syncComment: Comment = {
        id: `CMT-${Date.now()}-sync`,
        entityType: 'jobsheet',
        entityId: jobsheet.id,
        content: `Auto-completed due to service ${service.id} completion`,
        author: 'System',
        timestamp: new Date().toISOString()
      };

      updatedJobsheet = {
        ...updatedJobsheet,
        comments: [syncComment, ...updatedJobsheet.comments],
        updatedAt: new Date().toISOString()
      };
    }

    return { jobsheet: updatedJobsheet, changes };
  }

  static validateInvoiceIntegrity(invoice: Invoice, services: Service[], jobsheet?: Jobsheet): string[] {
    const issues: string[] = [];

    // Check if all services exist
    const existingServiceIds = services.map(s => s.id);
    const missingServices = invoice.serviceIds.filter(id => !existingServiceIds.includes(id));
    if (missingServices.length > 0) {
      issues.push(`Missing services: ${missingServices.join(', ')}`);
    }

    // Check if services are completed
    const incompleteServices = services.filter(s => 
      invoice.serviceIds.includes(s.id) && s.status !== 'completed'
    );
    if (incompleteServices.length > 0) {
      issues.push(`Incomplete services: ${incompleteServices.map(s => s.id).join(', ')}`);
    }

    // Validate cost calculation
    const servicesTotal = services
      .filter(s => invoice.serviceIds.includes(s.id))
      .reduce((sum, s) => sum + s.cost, 0);
    
    if (Math.abs(invoice.subtotal - servicesTotal) > 0.01) {
      issues.push(`Subtotal mismatch: invoice shows ${invoice.subtotal}, services total ${servicesTotal}`);
    }

    // Check jobsheet relationship
    if (jobsheet && invoice.jobsheetId !== jobsheet.id) {
      issues.push('Invoice jobsheet ID mismatch');
    }

    return issues;
  }

  static generateSyncReport(
    jobsheets: Jobsheet[], 
    services: Service[], 
    invoices: Invoice[]
  ): {
    orphanedServices: Service[];
    misalignedStatuses: { jobsheet: Jobsheet; service: Service }[];
    invoiceIssues: { invoice: Invoice; issues: string[] }[];
  } {
    const orphanedServices = services.filter(service => 
      !jobsheets.some(js => js.id === service.jobsheetId)
    );

    const misalignedStatuses: { jobsheet: Jobsheet; service: Service }[] = [];
    services.forEach(service => {
      const jobsheet = jobsheets.find(js => js.id === service.jobsheetId);
      if (jobsheet) {
        const expectedServiceStatus = this.syncJobsheetToService(jobsheet, service).service.status;
        if (service.status !== expectedServiceStatus) {
          misalignedStatuses.push({ jobsheet, service });
        }
      }
    });

    const invoiceIssues: { invoice: Invoice; issues: string[] }[] = [];
    invoices.forEach(invoice => {
      const relatedServices = services.filter(s => invoice.serviceIds.includes(s.id));
      const relatedJobsheet = jobsheets.find(js => js.id === invoice.jobsheetId);
      const issues = this.validateInvoiceIntegrity(invoice, relatedServices, relatedJobsheet);
      if (issues.length > 0) {
        invoiceIssues.push({ invoice, issues });
      }
    });

    return {
      orphanedServices,
      misalignedStatuses,
      invoiceIssues
    };
  }
}
