import React from "react";
import { useEnhancedAppContext } from "@/context";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { User, Eye, FileText } from "lucide-react";
import { Customer } from "@/types";
import { formatCurrency, formatDate } from "@/utils/dateUtils";

interface ViewCustomerDialogProps {
  customer: Customer | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ViewCustomerDialog = ({
  customer,
  open,
  onOpenChange,
}: ViewCustomerDialogProps) => {
  const { state } = useEnhancedAppContext();

  if (!customer) return null;

  const customerJobsheets = state.jobsheets.filter(
    (j) => j.customerId === customer.id
  );
  const customerInvoices = state.invoices.filter(
    (i) => i.customerId === customer.id
  );

  const totalInvoices = customerInvoices.length;
  const paidInvoices = customerInvoices.filter(
    (i) => i.status === "paid"
  ).length;
  const totalSpent = customerInvoices.reduce(
    (sum, invoice) => sum + invoice.total,
    0
  );
  const pendingInvoices = customerInvoices.filter(
    (i) => i.status !== "paid"
  ).length;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            View Customer Details - {customer.name}
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Customer Information */}
          <div className="lg:col-span-2 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>Customer Name</Label>
                <Input value={customer.name} readOnly className="mt-1" />
              </div>
              <div>
                <Label>Email Address</Label>
                <Input value={customer.email} readOnly className="mt-1" />
              </div>
              <div>
                <Label>Phone Number</Label>
                <Input value={customer.phone} readOnly className="mt-1" />
              </div>
              <div>
                <Label>Alternate Phone</Label>
                <Input
                  value={customer.alternatePhone || ""}
                  readOnly
                  className="mt-1"
                />
              </div>
            </div>

            <div>
              <Label>Address</Label>
              <Input value={customer.address} readOnly className="mt-1" />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label>State</Label>
                <Input value={customer.state || ""} readOnly className="mt-1" />
              </div>
              <div>
                <Label>City</Label>
                <Input value={customer.city || ""} readOnly className="mt-1" />
              </div>
              <div>
                <Label>Pincode</Label>
                <Input
                  value={customer.pincode || ""}
                  readOnly
                  className="mt-1"
                />
              </div>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-orange-500">
                    {totalInvoices}
                  </div>
                  <div className="text-sm text-gray-500">Total Invoices</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-green-500">
                    {paidInvoices}
                  </div>
                  <div className="text-sm text-gray-500">Paid Invoices</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-blue-500">
                    {formatCurrency(totalSpent)}
                  </div>
                  <div className="text-sm text-gray-500">Total Spent</div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4 text-center">
                  <div className="text-2xl font-bold text-purple-500">
                    {pendingInvoices}
                  </div>
                  <div className="text-sm text-gray-500">Pending Invoices</div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Customer Invoice History */}
          <div>
            <div className="flex items-center gap-2 mb-4">
              <FileText className="h-5 w-5" />
              <h3 className="text-lg font-semibold">
                Customer Invoice History
              </h3>
            </div>

            <div className="space-y-3 max-h-96 overflow-y-auto">
              {customerInvoices.length === 0 ? (
                <p className="text-gray-500">No invoices available</p>
              ) : (
                customerInvoices.map((invoice) => (
                  <Card key={invoice.id}>
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="font-medium">{invoice.id}</span>
                        <Badge
                          className={
                            invoice.status === "paid"
                              ? "bg-green-100 text-green-800"
                              : "bg-yellow-100 text-yellow-800"
                          }
                        >
                          {invoice.status}
                        </Badge>
                      </div>
                      <p className="text-sm text-gray-600">
                        {formatDate(invoice.issueDate)}
                      </p>
                      <p className="text-sm">
                        {customerJobsheets.find(
                          (j) => j.id === invoice.jobsheetId
                        )?.deviceType || "Unknown Device"}
                      </p>
                      <p className="font-semibold">
                        {formatCurrency(invoice.total)}
                      </p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-2 w-full"
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View
                      </Button>
                    </CardContent>
                  </Card>
                ))
              )}
            </div>
          </div>
        </div>

        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Close
          </Button>
          <Button className="bg-orange-500 hover:bg-orange-600">
            Print Customer Profile
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
