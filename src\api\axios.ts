import axios from 'axios';

const baseURL:string = import.meta.env.VITE_APP_SUPABASE_URL;

const instance = axios.create({
    baseURL,
});

instance.interceptors.request.use(
    (config) => {
        // Retrieve the branchId from localStorage
        const token = import.meta.env.VITE_APP_SUPABASE_ANON_KEY;

        if (token && (config.headers.Authorization == '' || config.headers.Authorization == null || config.headers.Authorization == undefined)) {
            // If branchId exists, add it to the request headers
            config.headers.Authorization = `Bearer ${token}`;
            config.headers.Apikey = `${token}`;
        }

        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

instance.interceptors.response.use(
    (response) => {
        console.log('Response Interceptor Triggered', response.status)
        return response;
    },
    (error) => {

        console.log('Response Interceptor Triggered', error, error?.response, error?.response?.status)
        if (error?.response && error?.response?.status === 401) {
            // Redirect to login page or perform any action you want
            // For example, if you're using Vue Router:
            // router.push('/login');
            localStorage.clear();
            window.location.reload();
            console.error('Unauthorized: Redirect to login page');
        } else if (error?.response && error?.response?.status == 512) {
            alert("Not allowed by CORS")
            // window.location.href = "/404"
            localStorage.clear();
            console.error('CORS Errpr: Redirect to login page');
        }
        return Promise.reject(error);
    }
);


export default instance;
