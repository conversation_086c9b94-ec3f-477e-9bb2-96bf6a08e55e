
import React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Jobsheet } from '@/types';
import { EditHistoryPanel } from './EditHistoryPanel';

interface ViewJobsheetDialogProps {
  jobsheet: Jobsheet;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const ViewJobsheetDialog = ({ jobsheet, open, onOpenChange }: ViewJobsheetDialogProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-orange-100 text-orange-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'delivered': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>View Jobsheet - {jobsheet.id}</DialogTitle>
        </DialogHeader>
        
        <div className="flex h-[calc(90vh-120px)]">
          <div className="flex-1 overflow-y-auto pr-4">
            <div className="space-y-4">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Customer Information</h3>
                <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                  <div>
                    <Label>Customer Name</Label>
                    <Input value={jobsheet.customerName} readOnly className="bg-white" />
                  </div>
                  <div>
                    <Label>Jobsheet ID</Label>
                    <Input value={jobsheet.id} readOnly className="bg-white" />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Device Information</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div>
                    <Label>Device Type</Label>
                    <Input value={jobsheet.deviceType} readOnly className="bg-gray-50" />
                  </div>
                  <div>
                    <Label>Brand</Label>
                    <Input value={jobsheet.brand} readOnly className="bg-gray-50" />
                  </div>
                  <div>
                    <Label>Model</Label>
                    <Input value={jobsheet.model} readOnly className="bg-gray-50" />
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Serial Number</Label>
                    <Input value={jobsheet.serialNumber || 'Not provided'} readOnly className="bg-gray-50" />
                  </div>
                  <div>
                    <Label>Device Password</Label>
                    <Input value={jobsheet.devicePassword || 'Not provided'} readOnly className="bg-gray-50" />
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Service Details</h3>
                <div>
                  <Label>Reported Issues</Label>
                  <Textarea value={jobsheet.reportedIssues} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Physical Condition</Label>
                  <Textarea value={jobsheet.physicalCondition} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Accessories Received</Label>
                  <Textarea value={jobsheet.accessoriesReceived} readOnly className="bg-gray-50" />
                </div>
                <div className="grid grid-cols-4 gap-4">
                  <div>
                    <Label>Estimated Cost</Label>
                    <Input value={`$${jobsheet.estimatedCost.toFixed(2)}`} readOnly className="bg-gray-50" />
                  </div>
                  <div>
                    <Label>Advance Payment</Label>
                    <Input value={`$${jobsheet.advancePayment.toFixed(2)}`} readOnly className="bg-gray-50" />
                  </div>
                  <div>
                    <Label>Expected Delivery</Label>
                    <Input value={new Date(jobsheet.expectedDelivery).toDateString()} readOnly className="bg-gray-50" />
                  </div>
                  <div>
                    <Label>Status</Label>
                    <div className="mt-2">
                      <Badge className={getStatusColor(jobsheet.status)}>
                        {jobsheet.status}
                      </Badge>
                    </div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Technician Assigned</Label>
                    <Input value={jobsheet.technicianAssigned || 'Not assigned'} readOnly className="bg-gray-50" />
                  </div>
                  <div>
                    <Label>Priority</Label>
                    <div className="mt-2">
                      <Badge className={getPriorityColor(jobsheet.priority)}>
                        {jobsheet.priority}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Additional Information</h3>
                <div>
                  <Label>Warranty Terms</Label>
                  <Textarea value={jobsheet.warrantyTerms || 'No warranty terms specified'} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Special Instructions</Label>
                  <Textarea value={jobsheet.specialInstructions || 'No special instructions'} readOnly className="bg-gray-50" />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Created At</Label>
                  <Input value={new Date(jobsheet.createdAt).toLocaleString()} readOnly className="bg-gray-50" />
                </div>
                <div>
                  <Label>Updated At</Label>
                  <Input value={new Date(jobsheet.updatedAt).toLocaleString()} readOnly className="bg-gray-50" />
                </div>
              </div>

              <Button onClick={() => onOpenChange(false)} className="w-full">
                Close
              </Button>
            </div>
          </div>

          <EditHistoryPanel
            entityType="jobsheet"
            entityId={jobsheet.id}
            comments={jobsheet.comments || []}
            onAddComment={() => {}} // Read-only mode
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};
